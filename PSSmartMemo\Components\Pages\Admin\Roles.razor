﻿@page "/admin/roles"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@inject AdminDataService Service
@inject SfDialogService DialogService
@inject CorporateService CpService


<SfToast @ref="_toastObj"></SfToast>
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Roles" Url=""></BreadcrumbItem>

    </BreadcrumbItems>
</SfBreadcrumb>
<SfDialog @ref="_dlgForm" Visible="false" Width="95vw" ShowClose="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>@_formTitle</Header>
        <Content>
            <EditForm Model="@_obj" OnValidSubmit="SaveData">
                <DataAnnotationsValidator/>
                <div class="row" style="align-items:center;">
                    @*<div class="col-md-2 mb-2">
                        <MudTextField Label="Code" ReadOnly="true" @bind-Value="obj.Code" />
                        </div>*@
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Auto" @bind-Value="_obj.Name"/>
                        <ValidationMessage For="@(() => _obj.Name)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Notes" FloatLabelType="FloatLabelType.Auto" @bind-Value="_obj.Notes"
                                   Multiline="true"/>
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row mb-2">
                    <div class="col-md">
                        <div class="mb-2">Active</div>
                        <SfSwitch @bind-Checked="_obj.IsActive"></SfSwitch> @_obj.Active
                    </div>
                </div>

                <div class="row">
                    <div class="col-md mb-2">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small"
                                   OnClick="@(async () => { await _dlgForm!.HideAsync(); })">
                            Cancel
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


<SfDialog @ref="_dlgFormUser" Visible="false" Width="95%" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Manage User on @_selectedRole Roles</Header>
        <Content>
            <div style="display:flex;gap:10px;">
                <div style="flex:1">
                    <MudText Typo="Typo.h5" Align="Align.Center">Available Users</MudText>
                    <SfGrid @ref="_dgMainAllUser" Width="100%" Height="400px" AllowTextWrap="true" DataSource="_allUsers"
                            AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridSelectionSettings
                            Type="SelectionType.Multiple">
                        </GridSelectionSettings>
                        <GridColumns>
                            <GridColumn Width="80px" HeaderText="Code" Field="@nameof(UserDTO.UserId)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Name" Field="@nameof(UserDTO.Name)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Email" Field="@nameof(UserDTO.Email)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Department"
                                        Field="@nameof(UserDTO.Department)">
                            </GridColumn>
                            <GridColumn Width="100px" HeaderText="Position"
                                        Field="@nameof(UserDTO.PosText)">
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
                <div class="mid-btn-panel" style="align-content: center !important;width:45px">
                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.ArrowRight"
                            OnClick="AddUser">
                    </MudFab>
                    &nbsp;
                    <MudFab Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.ArrowLeft"
                            OnClick="DelUser">
                    </MudFab>
                </div>
                <div style="flex:1">
                    <MudText Typo="Typo.h5" Align="Align.Center">Assigned Users</MudText>
                    <SfGrid @ref="_dgMainSelUser" Width="100%" Height="400px" AllowTextWrap="true" DataSource="_selUsers"
                            AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridSelectionSettings
                            Type="SelectionType.Multiple">
                        </GridSelectionSettings>
                        <GridColumns>
                            <GridColumn Width="80px" HeaderText="Code" Field="@nameof(UserDTO.UserId)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Name" Field="@nameof(UserDTO.Name)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Email" Field="@nameof(UserDTO.Email)"></GridColumn>
                            <GridColumn Width="100px" HeaderText="Department"
                                        Field="@nameof(UserDTO.Department)">
                            </GridColumn>
                            <GridColumn Width="100px" HeaderText="Position"
                                        Field="@nameof(UserDTO.PosText)">
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="_dlgFormMenu" Visible="false" Width="1100px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assigned Menu on @_selectedRole</Header>
        <Content>

            <div style="display:flex">
                <div>
                    <MudText Typo="Typo.h6" Align="Align.Center">Available Menu</MudText>
                    @*                     <SfGrid @ref="dgMainAllMenu" Height="400px" DataSource="allMenus" AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(MenuDTO.ModuleName)"></GridColumn>
                        <GridColumn HeaderText="Menu" Field="@nameof(MenuDTO.Name)"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Device" Field="@nameof(MenuDTO.Device)"></GridColumn>
                        </GridColumns>
                        </SfGrid> *@
                    <SfTreeGrid DataSource="_allMenus" @ref="_dgMainAllMenutrv" Height="400px"
                                IdMapping="@nameof(MenuDto.Id)" ParentIdMapping="@nameof(MenuDto.ParentId)"
                                TreeColumnIndex="0" AllowFiltering="true" AllowSorting="true">
                        <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel"/>
                        <TreeGridSelectionSettings
                            Type="SelectionType.Multiple">
                        </TreeGridSelectionSettings>
                        <TreeGridColumns>
                            <TreeGridColumn Width="300px" HeaderText="Name"
                                            Field="@nameof(MenuDto.Name)">
                            </TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Code" Format="000"
                                            Field="@nameof(MenuDto.Code)">
                            </TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Module"
                                            Field="@nameof(MenuDto.ModuleName)">
                            </TreeGridColumn>
                        </TreeGridColumns>
                    </SfTreeGrid>
                </div>
                <div class="mid-btn-panel" style="align-content: center !important;">
                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.ArrowRight"
                            OnClick="AddMenu">
                    </MudFab>
                    &nbsp;
                    <MudFab Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.ArrowLeft"
                            OnClick="DelMenu">
                    </MudFab>
                </div>
                <div>
                    <MudText Typo="Typo.h6" Align="Align.Center">Assigned Menu</MudText>
                    @* <SfGrid @ref="dgMainSelMenu" Height="400px" DataSource="selMenus" AllowSelection="true" AllowFiltering="true" AllowSorting="false">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(MenuDTO.ModuleName)"></GridColumn>
                        <GridColumn HeaderText="Menu" Field="@nameof(MenuDTO.Name)"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Device" Field="@nameof(MenuDTO.Device)"></GridColumn>
                        </GridColumns>
                        </SfGrid> *@
                    <SfTreeGrid DataSource="_selMenus" @ref="_dgMainSelMenutrv" Height="400px"
                                IdMapping="@nameof(MenuDto.Id)" ParentIdMapping="@nameof(MenuDto.ParentId)"
                                TreeColumnIndex="0" AllowFiltering="true" AllowSorting="true">
                        <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel"/>
                        <TreeGridSelectionSettings
                            Type="SelectionType.Multiple">
                        </TreeGridSelectionSettings>

                        <TreeGridColumns>
                            <TreeGridColumn Width="300px" HeaderText="Name"
                                            Field="@nameof(MenuDto.Name)">
                            </TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Code" Format="000"
                                            Field="@nameof(MenuDto.Code)">
                            </TreeGridColumn>
                            <TreeGridColumn Width="100px" HeaderText="Module"
                                            Field="@nameof(MenuDto.ModuleName)">
                            </TreeGridColumn>
                        </TreeGridColumns>
                    </SfTreeGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>


<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Application Roles Management</MudText>
    </div>
</div>

<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">
            Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">
            Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">
            Delete
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.Person"
                   Variant="Variant.Filled" OnClick="ManageUser">
            Users
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Menu" Variant="Variant.Filled"
                   OnClick="ManageMenu">
            Menus
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.Settings"
                   Variant="Variant.Filled" OnClick="OpenAssignRole">
            Sections
        </MudButton>
        @* <MudButton Size="Size.Small" Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.PostAdd" Variant="Variant.Filled" OnClick="ManageMenu">Posts</MudButton>  *@
    </div>

</div>
<div class="row">
    <div class="col-md">
        <SfGrid Height="calc(100vh - 220px)" @ref="_dgMain" DataSource="_allRoles" AllowSelection="true"
                AllowFiltering="true" AllowSorting="true" Width="100%">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" HeaderText="Code" Field="@nameof(RoleDTO.Code)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Name" Field="@nameof(RoleDTO.Name)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Active" Field="@nameof(RoleDTO.Active)"></GridColumn>
                <GridColumn CustomAttributes="@(new Dictionary<string, object> { { "class", "e-notecol" } })"
                            HeaderText="Notes" Field="@nameof(RoleDTO.Notes)">
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>
<style>
    .e-notecol {
        width: 400px !important;
    }
</style>

@code {
    private string _searchText = "";
    private List<RoleDTO> _allRoles = new();
    private List<UserDTO> _allUsers = new();
    private List<UserDTO> _selUsers = new();
    private List<MenuDto> _allMenus = new();
    private List<MenuDto> _selMenus = new();
    private RoleDTO _obj = new();
    private int _selectedRoleId;
    private string _selectedRole = "";
    private string _formTitle = "Add Role";
    private SfDialog? _dlgForm;
    private SfDialog? _dlgFormUser, _dlgFormMenu, _dlgFormSection;
    private SfGrid<RoleDTO>? _dgMain;
    private SfGrid<UserDTO>? _dgMainAllUser;
    private SfGrid<UserDTO>? _dgMainSelUser;
    private SfTreeGrid<MenuDto>? _dgMainAllMenutrv;
    private SfTreeGrid<MenuDto>? _dgMainSelMenutrv;
    private SfToast? _toastObj;
    private bool _isChecked = false;
    private bool _isFormEditable = true;
    private List<Employee2Dto> _allEmployees = new();

    private async Task OpenAssignRole()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var pId = _dgMain.SelectedRecords[0].Id;
                _selectedRoleId = pId;
                _selectedRole = _dgMain.SelectedRecords[0].Name;
                await _dlgFormSection!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select Role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task AddUser()
    {
        try
        {
            if (_dgMainAllUser!.SelectedRecords is { Count: > 0 })
            {
                var uId = _dgMainAllUser.SelectedRecords[0].Id;
                foreach (var item in _dgMainAllUser.SelectedRecords)
                {
                    var result = await Service.SaveUserRoleAsync(item.Id, _selectedRoleId);
                }

                await BindUsers(_selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task DelUser()
    {
        try
        {
            if (_dgMainSelUser!.SelectedRecords is { Count: > 0 })
            {

                foreach (var item in _dgMainSelUser.SelectedRecords)
                {
                    var result = await Service.DeleteUserRoleAsync(item.Id, _selectedRoleId);
                }

                //var result = await service.DeleteUserRoleAsync(uId, selectedRoleId);
                await BindUsers(_selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task BindUsers(int roleId)
    {
        _allUsers = await Service.GetAllActiveAsync();
        _selUsers = await Service.GetUserRoleAsync(roleId);

        var selUserIds = _selUsers.Select(x => x.Id).ToList();
        _allUsers = _allUsers.Where(x => !selUserIds.Contains(x.Id)).OrderBy(c => c.Name).ToList();
        _selUsers = _selUsers.OrderBy(c => c.Name).ToList();

        foreach (var au in _allUsers)
        {
            var m = _allEmployees.FirstOrDefault(m => au.UserId.EndsWith(m.EmpCode));
            if (m != null)
            {
                au.Department = m.DepartmentName;
                au.PosText = m.PosText;
            }
        }

        foreach (var au in _selUsers)
        {
            var m = _allEmployees.FirstOrDefault(m => au.UserId.EndsWith(m.EmpCode));
            if (m != null)
            {
                au.Department = m.DepartmentName;
                au.PosText = m.PosText;
            }
        }
    }

    private async Task ManageUser()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = _dgMain.SelectedRecords[0].Id;
                _selectedRoleId = id;
                _selectedRole = _dgMain.SelectedRecords[0].Name;
                await BindUsers(id);
                await _dlgFormUser!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task ManageMenu()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = _dgMain.SelectedRecords[0].Id;
                _selectedRoleId = id;
                _selectedRole = _dgMain.SelectedRecords[0].Name;
                await BindMenus(id);
                await _dlgFormMenu!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task AddMenu()
    {
        try
        {
            var selectedRec = await _dgMainAllMenutrv!.GetSelectedRecordsAsync();
            if (selectedRec is { Count: > 0 })
            {
                foreach (var mi in selectedRec)
                {
                    var result = await Service.SaveRoleMenuAsync(mi.Id, _selectedRoleId);
                }

                await BindMenus(_selectedRoleId);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task DelMenu()
    {
        try
        {
            var selectedRec = await _dgMainSelMenutrv!.GetSelectedRecordsAsync();
            foreach (var mi in selectedRec)
            {
                var result = await Service.DeleteRoleMenuAsync(mi.Id, _selectedRoleId);
            }

            await BindMenus(_selectedRoleId);
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task BindMenus(int roleId)
    {
        _allMenus = await Service.GetAllActiveMenusAsync();
        _selMenus = await Service.GetRoleMenuAsync(roleId);
    }



    protected override async Task OnInitializedAsync()
    {
        try
        {
            _allRoles = await Service.GetAllRolesAsync();
            _allEmployees = await CpService.GetAllEmployeeData();
            var res = await CpService.UpdateUsers(_allEmployees);
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenCreateForm()
    {
        _isFormEditable = true;
        _formTitle = "Add Role";
        try
        {
            _obj = new RoleDTO { IsActive = false };
            await _dlgForm!.ShowAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }


    public async Task SaveData()
    {
        try
        {
            var vm = await Service.IsRoleValid(_obj);
            if (vm == "OK")
            {
                var res = await Service.SaveRoleAsync(_obj, 1);
                _allRoles = await Service.GetAllRolesAsync();
                await _dlgForm!.HideAsync();
            }
            else
            {
                var tm = new ToastModel { Content = vm, Title = "Error", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenEditForm()
    {
        _formTitle = "Edit Role";
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = _dgMain.SelectedRecords[0].Id;
                _obj = await Service.GetRoleAsync(id);
                await _dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            if (_dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                var id = _dgMain.SelectedRecords[0].Id;
                if (conf)
                {
                    var res = await Service.DeleteRoleAsync(id);
                    if (res == "OK")
                    {
                        _allRoles = await Service.GetAllRolesAsync();
                    }
                    else
                    {
                        var mm = new ToastModel { Content = "The entity cannot be deleted because it is being used by other entities.", Title = "Error", ShowCloseButton = true, Timeout = 0 };
                        await _toastObj!.ShowAsync(mm);
                    }
                }
            }
            else
            {
                var tm = new ToastModel { Content = "Please select a role.", Title = "Info", ShowCloseButton = true, Timeout = 0 };
                await _toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await _toastObj!.ShowAsync(tm);
        }
    }

}

<style>
    .e-grid td.e-active {
        background: #faa601 !important;
    }
</style>
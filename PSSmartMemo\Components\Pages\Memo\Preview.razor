﻿@layout PreviewLayout
@page "/memos/{MemoId:int}/preview"
@using PSSmartMemo.Components.Layout
@inject IJSRuntime JS
@inject MemoDataService Service
@inject WorklistDataService WLService
@rendermode InteractiveServer
@inject IWebHostEnvironment WebHostEnvironment
@inject NavigationManager NavigationManager
@inject NavigationManager NavMgr

<link href="~/css/print-preview.css" rel="stylesheet" />
<script src="~/js/print-optimization.js"></script>

<div class="print-optimized-container" id="printable-content">
    <!-- Header Section - Keep together on print -->
    <div class="print-header-section">
        <div class="header-content">
            <div class="memo-header-info">
                <div class="logo-container">
                    <img src="images/logo.png" style="height: 20px" alt="logo"/>
                </div>
                <div class="memo-code">@_memoObj?.MemoCode</div>
                <h1 class="memo-title">@(_memoObj?.MemoTitle?.ToUpper())</h1>
                <div class="memo-meta">
                    <div class="memo-initiated">Initiated By: @_memoObj?.InitiatedBy</div>
                    <div class="memo-initiated">Initiated On: @_memoObj?.MemoCreatedDate?.ToString("d MMM, yyyy")</div>
                </div>
            </div>
            <div class="memo-details-table">
                <table>
                    <tr>
                        <td><strong>Department:</strong></td>
                        <td>@_memoObj?.Department</td>
                    </tr>
                    <tr>
                        <td><strong>Division:</strong></td>
                        <td>@_memoObj?.Division</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Main Content Sections -->
    <div class="print-content-sections">
        @if (_memoObj?.MemoSections != null)
        {
            @foreach (var sec in _memoObj.MemoSections.Where(c => c.MemoSectionIgnored == false))
            {
                <div class="print-section-block">
                    <div class="print-section-header">
                        <h3 class="print-section-title">@sec.MemoSectionTitle</h3>
                    </div>
                    <div class="print-section-content">
                        @((MarkupString)sec.MemoSectionContentHtml!)
                    </div>
                </div>
            }
        }

        <!-- Attachments Section -->
        @if (_attachments.Any())
        {
            <div class="print-section-block">
                <div class="print-section-header">
                    <h3 class="print-section-title">Attachments</h3>
                </div>
                <div class="print-section-content">
                    <table class="print-attachments-table">
                        <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Description</th>
                            <th class="no-print">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach (var file in _attachments)
                        {
                            <tr>
                                <td>@file.Name</td>
                                <td>@file.AttachmentType</td>
                                <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                                <td>@file.Description</td>
                                <td class="no-print">
                                    <MudLink Href="@file.Path" Target="_blank" Disabled="@(file.Path.StartsWith("http"))">
                                        <MudIcon Icon="@Icons.Material.Filled.Download"/>
                                        Download
                                    </MudLink>
                                </td>
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
        }

        <!-- Approvers Section -->
        <div class="print-section-block print-approvers-section">
            <div class="print-section-header">
                <h3 class="print-section-title">Approvers</h3>
            </div>
            <div class="print-section-content">
                <PSSmartMemo.Components.Shared.ApproversPopup MemoId="MemoId"></PSSmartMemo.Components.Shared.ApproversPopup>
            </div>
        </div>
    </div>

    <!-- Action Buttons - Hidden in print -->
    <div class="action-buttons no-print">
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Print"
                   OnClick="Print"
                   Class="print-action-button">
            Print Memo
        </MudButton>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   StartIcon="@Icons.Material.Filled.ArrowBack"
                   OnClick="GoBack"
                   Class="back-action-button">
            Back
        </MudButton>
    </div>
</div>

@code {
    [Parameter] public int MemoId { get; set; }
    private MemoDto? _memoObj;
    private string _userId = "";
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }

        //var isMyMemo = await Service.IsMyMemo(MemoId, _userId);
        //if (!isMyMemo)
        //{
        //    NavMgr.NavigateTo("/");
        //}

        _memoObj = await Service.GetMemoPreview(MemoId);
        _approvalLogs = await WLService.GetMemoApprovalLogs(MemoId);
        _attachments = await Service.GetMemoAttachments(MemoId);
        //(DivisionName, DepartmentName) = await Service.GetUserDivisionAndDeptByMemoId(MemoId);
    }

    private async Task Print()
    {
        // Add a small delay to ensure all content is rendered
        await Task.Delay(100);

        // Optimize for print
        await JS.InvokeVoidAsync("optimizeForPrint");

        // Trigger print
        await JS.InvokeVoidAsync("window.print");
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/");
    }


    private List<MemoApprovalLogDto> _approvalLogs = new();

    private List<MemoAttachmentDto> _attachments = new();

    private string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB", "TB"];
        var order = 0;
        double size = bytes;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

}
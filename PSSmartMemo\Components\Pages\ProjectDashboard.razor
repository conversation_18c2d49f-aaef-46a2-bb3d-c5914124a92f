@page "/project-dashboard"
@inject CorporateService CorpService
@attribute [Authorize]
@rendermode InteractiveServer
@inject NavigationManager NavMgr

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Project Dashboard" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="dashboard-container">
    <div class="dashboard-header">
        <div class="header-title">
            <MudIcon Icon="@Icons.Material.Filled.Dashboard" Size="Size.Medium" Class="mr-2"/>
            <h2>Project Dashboard</h2>
        </div>
        <div class="header-actions">
            <MudTooltip Text="Refresh Projects">
                <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                               Size="Size.Small"
                               OnClick="RefreshProjects"
                               Class="refresh-button"/>
            </MudTooltip>
        </div>
    </div>

    <div class="projects-grid">
        @if (_isLoading)
        {
            <div class="loading-container">
                <MudProgressCircular Color="Color.Primary" Indeterminate="true"/>
                <span>Loading projects...</span>
            </div>
        }
        else if (_projects.Count == 0)
        {
            <div class="no-projects">
                <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Large"/>
                <p>No projects found requiring your approval.</p>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="RefreshProjects">
                    Refresh
                </MudButton>
            </div>
        }
        else
        {
            @foreach (var project in _projects.OrderBy(c => c.ProjectName.Trim()).ToList())
            {
                <div class="project-card" @onclick="() => NavigateToProject(project)">
                    <div class="project-icon-container @GetProjectColorClass(project.ProjectCode)">
                        <div class="project-icon">
                            @if (project.ProjectCode == "MMS")
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Large"/>
                            }
                            else if (project.ProjectCode == "RMS")
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Large"/>
                            }
                            else if (project.ProjectCode == "TRS")
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large"/>
                            }
                            else
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Folder" Size="Size.Large"/>
                            }
                        </div>
                    </div>
                    <div class="project-details">
                        <h3 class="project-name">@project.ProjectName</h3>

                        <div class="approval-counts">
                            <div class="count-item">
                                <span class="count-label">Direct:</span>
                                <span class="count-value">@project.Direct</span>
                            </div>
                            <div class="count-item">
                                <span class="count-label">Indirect:</span>
                                <span class="count-value">@project.Indirect</span>
                            </div>
                            <div class="count-item total">
                                <span class="count-label">Total:</span>
                                <span class="count-value">@project.Total</span>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? AuthState { get; set; }
    private List<PendingApprovalDto> _projects = new();
    private bool _isLoading = true;
    private string _userId = "";

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
                await RefreshProjects();
            }
        }
    }

    private async Task RefreshProjects()
    {
        _isLoading = true;
        StateHasChanged();

        try
        {
            _projects = await CorpService.GetEmpPendingApprovals(_userId);

            // Add Memo project if it doesn't exist
            if (!_projects.Any(p => p.ProjectCode == "MMS"))
            {
                _projects.Add(new PendingApprovalDto
                {
                    ProjectCode = "MMS",
                    ProjectName = "Memo",
                    Direct = 0,
                    Indirect = 0,
                    Total = 0,
                    Link = "/memo-approvals"
                });
            }

            // Add RINGI project if it doesn't exist
            if (!_projects.Any(p => p.ProjectCode == "RMS"))
            {
                _projects.Add(new PendingApprovalDto
                {
                    ProjectCode = "RMS",
                    ProjectName = "RINGI",
                    Direct = 0,
                    Indirect = 0,
                    Total = 0,
                    Link = "/ringi-approvals"
                });
            }

            _projects = _projects.OrderBy(x => x.ProjectCode).ToList();
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void NavigateToProject(PendingApprovalDto project)
    {
        if (project.ProjectCode == "MMS")
        {
            NavMgr.NavigateTo("/memo-approvals");
        }
        else if (project.ProjectCode == "RMS")
        {
            NavMgr.NavigateTo("/ringi-approvals");
        }
        else if (!string.IsNullOrEmpty(project.Link))
        {
            NavMgr.NavigateTo(project.Link);
        }
    }

    private string GetProjectColorClass(string projectCode)
    {
        return projectCode switch
        {
            "MMS" => "memo-color",
            "RMS" => "ringi-color",
            "TRS" => "trs-color",
            _ => "default-color"
        };
    }

}
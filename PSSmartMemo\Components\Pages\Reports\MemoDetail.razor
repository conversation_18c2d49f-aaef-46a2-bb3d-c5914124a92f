@page "/reports/memos/{Id:int}/detail"
@using PSSmartMemo.Components.Shared
@inject ReportMemoDataService Service
@inject WorklistDataService WorklistService
@inject MemoDataService MemoService
@inject NavigationManager NavMgr
@attribute [Authorize]
@rendermode InteractiveServer

<style>
    .page-block {
        margin-top: 40px;
        padding: 30px;
        padding-left: 30px;
        padding-right: 30px;
        background-color: white;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        border: 1px solid gray
    }

    .approvers-dialog-content {
        padding: 0;
    }

    .dialog-footer {
        padding: 10px;
        text-align: right;
    }

    .tmp-preview-container {
        padding: 15px;
        max-width: 100% !important;
        margin: 0 !important;
        margin-bottom: 30px !important;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid #aaa;
    }

        .tmp-preview-container:last-child {
            margin-bottom: 0 !important;
        }

    .memo-header-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

        .memo-header-info img {
            height: 26px !important;
            width: auto;
        }

    .memo-code {
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }

    .memo-title {
        font-size: 24px;
        margin: 0;
        color: #333;
    }

    .memo-initiated {
        font-size: 13px;
        color: #666;
        font-style: italic;
    }

    .tmp-preview-section-content {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
        margin-bottom: 15px !important;
    }

    .approver-table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 15px !important;
    }

        .approver-table td,
        .approver-table th {
            border: 1px solid #000 !important;
            padding: 6px !important;
        }

    .tmp-preview-section-block {
        margin-bottom: 20px;
    }

    .tmp-preview-section-header {
        margin-bottom: 10px;
    }

    .tmp-preview-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
</style>

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Reports" Url="/reports"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url="/reports/memos"></BreadcrumbItem>
        <BreadcrumbItem Text="@(_memoDetail?.Title ?? "Detail")" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

@if (isLoading)
{
    <div class="text-center p-4">
        <MudProgressCircular Indeterminate="true" />
        <p class="mt-2">Loading memo details...</p>
    </div>
}
else if (_memoDetail == null)
{
    <div class="alert alert-warning">
        <h6>Memo Not Found</h6>
        <p class="mb-0">The requested memo was not found or you don't have access to view it.</p>
        <MudButton Variant="Variant.Text" Color="Color.Primary" OnClick="@(() => NavMgr.NavigateTo("/reports/memos"))" Class="mt-2">
            Back to Memos
        </MudButton>
    </div>
}
else
{
    <div class="tmp-preview-container">
        <div class="tmp-preview-header">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 40px; padding: 10px;">
                <div class="memo-header-info">
                    <div>
                        <img src="images/logo.png" style="height: 20px" alt="logo" />
                    </div>
                    <span class="memo-code">Memo ID: @_memoDetail.MemoId</span>
                    <h1 class="memo-title">@(_memoDetail.Title?.ToUpper() ?? "")</h1>
                    <div>
                        <div class="memo-initiated">Template: @_memoDetail.Template</div>
                    </div>
                </div>
                <div>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               OnClick="@(() => NavMgr.NavigateTo("/reports/memos"))"
                               StartIcon="@Icons.Material.Filled.ArrowBack">
                        Back to Memos
                    </MudButton>
                </div>
            </div>
        </div>

        <div class="tmp-preview-sections-wrapper">
            @foreach (var sec in _memoDetail.Sections.OrderBy(s => s.SectionSortOrder))
            {
                <div class="tmp-preview-section-block">
                    <div class="tmp-preview-section-header">
                        <h3 class="tmp-preview-section-title">@sec.TemplateSectionTitle</h3>
                    </div>
                    <div class="tmp-preview-section-content">
                        @((MarkupString)(sec.ContentHtml ?? ""))
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="tmp-preview-container">
        <div class="tmp-preview-section-block">
            <div class="tmp-preview-section-header">
                <h3 class="tmp-preview-section-title">Attachments</h3>
            </div>
            @if (_memoDetail.Attachments.Any())
            {
                <table class="approver-table">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Title</th>
                            <th>Size</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var file in _memoDetail.Attachments)
                        {
                            <tr>
                                <td>@file.Name</td>
                                <td>@file.Description</td>
                                <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(file.Path))
                                    {
                                        <MudLink Href="@file.Path" Target="_blank">
                                            <MudIcon Icon="@Icons.Material.Filled.Download" />
                                            Download
                                        </MudLink>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <MudText Typo="Typo.body2" Color="Color.Secondary">No attachments available</MudText>
            }
        </div>
    </div>

    <div class="tmp-preview-container">
        <div class="tmp-preview-section-block">
            <div class="tmp-preview-section-header" style="display: flex; justify-content:space-between">
                <h3 class="tmp-preview-section-title">Approvers</h3>
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Primary"
                           Size="Size.Small"
                           OnClick="OpenApproversDialog">
                    View Approval Logs
                </MudButton>
            </div>
            <PSSmartMemo.Components.Shared.ApproversPopup MemoId="Id"></PSSmartMemo.Components.Shared.ApproversPopup>
        </div>
    </div>
}

<SfDialog @ref="_approversDialog" Width="800px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>Memo Approval Logs</Header>
        <Content>
            <div class="approvers-dialog-content">
                <ApprovalTimeline ApprovalLogs="@approvalLogs" />
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="e-primary" OnClick="@(() => _approversDialog.HideAsync())">Close</SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>


@code {
    [Parameter] public int Id { get; set; }
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private List<MemoApprovalLogDto> approvalLogs = new();
    private SfDialog _approversDialog;

    private string userId = "";
    private bool isLoading = true;
    private MemoDetailDto? _memoDetail;



    protected override async Task OnParametersSetAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
                return;
            }
        }

        await LoadMemoDetail();
    }

    private async Task LoadMemoDetail()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            _memoDetail = await Service.GetMemoDetailAsync(Id, userId);
            approvalLogs = await WorklistService.GetMemoApprovalLogs(Id);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading memo detail: {ex.Message}");
            _memoDetail = null;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB", "TB"];
        var order = 0;
        double size = bytes;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

    private async Task OpenApproversDialog()
    {
        await _approversDialog.ShowAsync();
    }
}

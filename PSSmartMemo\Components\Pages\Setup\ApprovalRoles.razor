@page "/setup/approver-roles"
@inject ApprovalRoleDataService Service
@using FilterType = Syncfusion.Blazor.Grids.FilterType

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval Roles" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Approval Roles</MudText>
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@Icons.Material.Filled.Add">
        Create
    </MudButton>
</div>

<SfGrid DataSource="@_approvalRoles" AllowSorting="true" AllowFiltering="true" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="Title" HeaderText="Title" Width="150"></GridColumn>
        <GridColumn Field="Type" HeaderText="Type" Width="120"></GridColumn>
        <GridColumn Field="UserName" HeaderText="User Name" Width="150"></GridColumn>
        <GridColumn Field="Department" HeaderText="Department" Width="150"></GridColumn>
        <GridColumn Field="Designation" HeaderText="Designation" Width="150"></GridColumn>
        <GridColumn Field="IsActive" HeaderText="Active" Width="100" TextAlign="TextAlign.Center">
            <Template>
                @{
                    var role = context as ApprovalRoleDto;
                    @if (role?.IsActive == true)
                    {
                        <span class="badge bg-success">Active</span>
                    }
                    else
                    {
                        <span class="badge bg-danger">Inactive</span>
                    }
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Actions" Width="120" TextAlign="TextAlign.Center">
            <Template Context="cc">
                @{
                    if (cc is ApprovalRoleDto obj)
                    {
                        if (obj.Title != "Initiator" && obj.Title != "Generic")
                        {
                            <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(() => EditRole(obj))"></SfButton>
                            <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(() => DeleteRole(obj))"></SfButton>
                        }
                    }
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="_dialog" Width="700px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@_dialogTitle</Header>
        <Content>
            <EditForm Model="@_role" OnValidSubmit="SaveRole">
                <DataAnnotationsValidator/>
                <ValidationSummary/>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" @bind-Value="_role.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _role.Title)"/>
                    </div>

                    <div class="col-md">
                        <SfDropDownList DataSource="_typeList" @bind-Value="_role.Type" Placeholder="Type" FloatLabelType="FloatLabelType.Always">

                        </SfDropDownList>

                        <ValidationMessage For="@(() => _role.Type)"/>
                    </div>
                </div>
                @if (_role.Type == "Fixed")
                {
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Domain ID" @bind-Value="_role.UserId" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.UserId)"/>
                        </div>
                        <div class="col-md">
                            <SfTextBox Placeholder="User Name" @bind-Value="_role.UserName" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.UserName)"/>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Email" @bind-Value="_role.UserEmail" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.UserEmail)"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Department" @bind-Value="_role.Department" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.Department)"/>
                        </div>
                        <div class="col-md">
                            <SfTextBox Placeholder="Designation" @bind-Value="_role.Designation" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.Designation)"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md-6">
                            <SfTextBox Placeholder="HCM Code" @bind-Value="_role.HCMCode" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _role.HCMCode)"/>
                        </div>
                    </div>
                }
                <div class="row mb-3">
                    <div class="col-md" style="display:flex; gap:10px;flex-direction:column;justify-content:space-between; gap:5px;">
                        <label>Is Active</label>
                        <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="_role.IsActive" Label="Is Active"></SfSwitch>
                    </div>

                </div>

                <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string _userId = "";
    private List<ApprovalRoleDto> _approvalRoles = new();
    private ApprovalRoleDto _role = new();
    private string _dialogTitle = "Add Approval Role";
    private SfDialog? _dialog;

    protected override async Task OnInitializedAsync()
    {
        _approvalRoles = await Service.GetTemplateRolesAsync();
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task OpenCreateForm()
    {
        _role = new ApprovalRoleDto();
        _dialogTitle = "Add Approval Role";
        if (_dialog != null) await _dialog.ShowAsync();
    }

    private async Task EditRole(ApprovalRoleDto roleDto)
    {
        _role = roleDto;
        _dialogTitle = "Edit Approval Role";
        if (_dialog != null) await _dialog.ShowAsync();
    }

    private async Task SaveRole()
    {
        var result = await Service.Save(_role, _userId);
        if (result.Item2 == "OK")
        {
            _approvalRoles = await Service.GetTemplateRolesAsync();
            if (_dialog != null) await _dialog.HideAsync();
        }
        // Handle error
        // You might want to show an error message using a toast or alert
    }

    private async Task DeleteRole(ApprovalRoleDto roleDto)
    {
        var result = await Service.Delete(roleDto.Id, _userId);
        if (result == "OK")
        {
            _approvalRoles = await Service.GetTemplateRolesAsync();
        }
        // Handle error
        // You might want to show an error message using a toast or alert
    }

    private readonly string[] _typeList = ["Dynamic", "Fixed", "Generic"];
}
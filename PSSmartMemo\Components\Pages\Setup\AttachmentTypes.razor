﻿@page "/setup/attachment-types"
@inject AttachmentTypeDataService Service
@inject MemoTypeDataService MemoTypeService
@using FilterType = Syncfusion.Blazor.Grids.FilterType

<SfToast @ref="_toastObj"></SfToast>

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Attachment Types" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Attachment Types</MudText>
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@Icons.Material.Filled.Add">
        Create
    </MudButton>
</div>

<SfGrid DataSource="@_attachmentTypes" AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="Title" HeaderText="Title" AutoFit="true"></GridColumn>
        <GridColumn Field="MemoType" HeaderText="Memo Type" AutoFit="true"></GridColumn>
        <GridColumn Field="Description" HeaderText="Description" AutoFit="true"></GridColumn>
        <GridColumn Field="Status" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" AutoFit="true" TextAlign="TextAlign.Center">
            <Template>
                <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(_ => EditAttachmentType(context as AttachmentTypeDto))"></SfButton>
                <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(_ => DeleteAttachmentType(context as AttachmentTypeDto))"></SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dialog" Width="500px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@_dialogTitle</Header>
        <Content>
            <EditForm Model="@_attachmentType" OnValidSubmit="SaveAttachmentType">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfDropDownList TValue="int?" TItem="MemoTypeDto"
                                        @bind-Value="_attachmentType.MemoTypeId"
                                        DataSource="@_memoTypes"
                                        Placeholder="Memo Type"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => _attachmentType.MemoTypeId)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" @bind-Value="_attachmentType.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _attachmentType.Title)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Multiline="true" Placeholder="Description" @bind-Value="_attachmentType.Description" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <div style="display:flex; flex-direction: column;">
                            <label>Active</label>
                            <SfSwitch @bind-Checked="_attachmentType.IsActive" OnLabel="Yes" OffLabel="No"></SfSwitch>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md">
                        <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private SfToast? _toastObj;
    private string _userId = "";
    private List<AttachmentTypeDto> _attachmentTypes = new();
    private List<MemoTypeDto> _memoTypes = new();
    private AttachmentTypeDto _attachmentType = new();
    private string _dialogTitle = "Add Attachment Type";
    private SfGrid<AttachmentTypeDto> grid;
    private SfDialog dialog;
    private int? selectedMemoTypeId;

    protected override async Task OnInitializedAsync()
    {
        _memoTypes = await MemoTypeService.GetAll();
        await LoadAttachmentTypes();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task LoadAttachmentTypes()
    {
        _attachmentTypes = await Service.GetAll();
        if (selectedMemoTypeId.HasValue)
        {
            _attachmentTypes = _attachmentTypes.Where(x => x.MemoTypeId == selectedMemoTypeId).ToList();
        }
    }


    private async Task OpenCreateForm()
    {
        _attachmentType = new AttachmentTypeDto
        {
            IsActive = true,
            MemoTypeId = selectedMemoTypeId
        };
        _dialogTitle = "Add Attachment Type";
        await dialog.ShowAsync();
    }

    private async Task EditAttachmentType(AttachmentTypeDto dto)
    {
        _attachmentType = dto;
        _dialogTitle = "Edit Attachment Type";
        await dialog.ShowAsync();
    }

    private async Task SaveAttachmentType()
    {
        var result = await Service.Save(_attachmentType, _userId);
        if (result.Item2 == "OK")
        {
            await LoadAttachmentTypes();
            await dialog.HideAsync();
        }
        else
        {
            var tm = new ToastModel { Content = result.Item2, Timeout = 5000, ShowCloseButton = true, ShowProgressBar = true, Title = "Error" };
            if (_toastObj != null)
            {
                await _toastObj.ShowAsync(tm);
            }
            // Handle error - you might want to add a toast notification here
        }
    }

    private async Task DeleteAttachmentType(AttachmentTypeDto dto)
    {
        var result = await Service.DeleteById(dto.Id, _userId);
        if (result == "OK")
        {
            await LoadAttachmentTypes();
        }
        else
        {
            // Handle error - you might want to add a toast notification here
            var tm = new ToastModel { Content = result, Timeout = 5000, ShowCloseButton = true, ShowProgressBar = true, Title = "Error" };
            if (_toastObj != null)
            {
                await _toastObj.ShowAsync(tm);
            }
        }
    }

}
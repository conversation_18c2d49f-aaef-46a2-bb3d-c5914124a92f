@page "/watchlist/viewmemo/{Id}"
@using PSSmartMemo.Components.Shared
@inject WatchListDataService Service
@inject WorklistDataService WorklistService
@inject IDialogService DialogService
@inject MemoDataService MemoService

@attribute [Authorize]

<style>
    .page-block {
        margin-top: 40px;
        padding: 30px;
        padding-left: 30px;
        padding-right: 30px;
        background-color: white;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        border: 1px solid gray
    }

    .approvers-dialog-content {
        padding: 0;
    }
    
    .dialog-footer {
        padding: 10px;
        text-align: right;
    }

    .tmp-preview-container {
        padding: 15px;
        max-width: 100% !important;
        margin: 0 !important;
        margin-bottom: 30px !important;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid #aaa;
    }

    .tmp-preview-container:last-child {
        margin-bottom: 0 !important;
    }

    .memo-header-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .memo-header-info img {
        height: 26px !important;
        width: auto;
    }

    .memo-code {
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }

    .memo-title {
        font-size: 24px;
        margin: 0;
        color: #333;
    }

    .memo-initiated {
        font-size: 13px;
        color: #666;
        font-style: italic;
    }

    .tmp-preview-section-content {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
        margin-bottom: 15px !important;
    }

    .approver-table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 15px !important;
    }

    .approver-table td,
    .approver-table th {
        border: 1px solid #000 !important;
        padding: 6px !important;
    }

    .no-print,
    .action-buttons,
    .navigation-controls {
        display: none !important;
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }

    .tmp-preview-section-block {
        margin-bottom: 20px;
    }

    .tmp-preview-section-header {
        margin-bottom: 10px;
    }

    .tmp-preview-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
</style>

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Watch list" Url="/watchlist"></BreadcrumbItem>
        <BreadcrumbItem Text="@_memoObj.MemoTitle" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="tmp-preview-container">
    <div class="tmp-preview-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 40px; padding: 10px;">
            <div class="memo-header-info">
                <div>
                    <img src="images/logo.png" style="height: 20px" alt="logo"/>
                </div>
                <span class="memo-code">@_memoObj.MemoCode</span>
                <h1 class="memo-title">@(_memoObj.MemoTitle?.ToUpper() ?? "")</h1>
                <div>

                    <div class="memo-initiated">Initiated by: @_memoObj.InitiatedBy</div>
                    <div class="memo-initiated">Initiated On: @_memoObj?.MemoCreatedDate?.ToString("d MMM, yyyy")</div>
                </div>

            </div>
            <table style="width:400px">
                <tr>
                    <td style="width:120px"><b>Department:</b>&nbsp;</td><td>@_memoObj.Department</td>
                </tr>
                <tr>
                    <td><b>Division:</b>&nbsp;</td><td>@_memoObj.Division</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="tmp-preview-sections-wrapper">
        @foreach (var sec in _memoObj.MemoSections.Where(s => !s.MemoSectionIgnored))
        {
            <div class="tmp-preview-section-block">
                <div class="tmp-preview-section-header">
                    <h3 class="tmp-preview-section-title">@sec.MemoSectionTitle</h3>
                </div>
                <div class="tmp-preview-section-content">
                    @((MarkupString)sec.MemoSectionContentHtml!)
                </div>
            </div>
        }
    </div>
</div>

<div class="tmp-preview-container">
    <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header">
            <h3 class="tmp-preview-section-title">Attachments</h3>
        </div>
        @if (_attachments.Any())
        {
            <table class="approver-table">
                <thead>
                <tr>
                    <th>File Name</th>
                    <th>Type</th>
                    <th>Size</th>
                    <th>Description</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                @foreach (var file in _attachments)
                {
                    <tr>
                        <td>@file.Name</td>
                        <td>@file.AttachmentType</td>
                        <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                        <td>@file.Description</td>
                        <td>
                            <MudLink Href="@file.Path" Target="_blank">
                                <MudIcon Icon="@Icons.Material.Filled.Download"/>
                                Download
                            </MudLink>
                        </td>
                    </tr>
                }
                </tbody>
            </table>
        }
        else
        {
            <MudText Typo="Typo.body2" Color="Color.Secondary">No attachments available</MudText>
        }
    </div>
</div>

<div class="tmp-preview-container">
    <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header" style="display:flex;justify-content:space-between;align-items:center">
            <h3 class="tmp-preview-section-title">Approvers</h3>
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Primary"
                       Size="Size.Small"
                       OnClick="OpenApproversDialog">
                View Approval Logs
            </MudButton>
        </div>
        <div class="tmp-preview-section-content">
            <ApproversPopup MemoId="@_memoObj.MemoId"/>
        </div>
    </div>
</div>

<SfDialog @ref="approversDialog" Width="800px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>Memo Approval Logs</Header>
        <Content>
            <div class="approvers-dialog-content">
                <ApprovalTimeline ApprovalLogs="@_approvalLogs"/>
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="e-primary" OnClick="@(() => approversDialog.HideAsync())">Close</SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public string? Id { get; set; }
    private int _memoId;
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string _userId = "";
    private MemoDto _memoObj = new();
    private List<MemoApprovalLogDto> _approvalLogs = new();
    private List<MemoAttachmentDto> _attachments = new();
    private SfDialog approversDialog;

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        if (string.IsNullOrEmpty(_userId))
        {
            NavMgr.NavigateTo("/");
        }

        try
        {
            if (Id != null)
            {
                _memoId = Convert.ToInt32(Id);
            }
        }
        catch (Exception)
        {
            _memoId = 0;
        }

        if (_memoId == 0)
        {
            NavMgr.NavigateTo("/watchlist");
        }

        var watchMemo = await Service.GetWatchMemoDetail(_memoId, _userId);
        if (watchMemo == null)
        {
            NavMgr.NavigateTo("/watchlist");
            return;
        }

        _memoObj = watchMemo;
        _approvalLogs = await WorklistService.GetMemoApprovalLogs(_memoObj.MemoId);
        _attachments = await Service.GetMemoAttachments(_memoObj.MemoId);

        // Get department and division values
        var (division, department) = await MemoService.GetUserDivisionAndDeptByMemoId(_memoObj.MemoId);
        _memoObj.Division = division;
        _memoObj.Department = department;
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB", "TB"];
        var order = 0;
        double size = bytes;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

    private async Task OpenApproversDialog()
    {
        await approversDialog.ShowAsync();
    }

}
﻿@inject WorklistDataService Service
@inject DelegationDataService DelegationService
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType

@*<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>*@
@* <div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Approval list</MudText>

</div> *@


<div class="row">
    <div class="col-md">
        <SfTab Height="calc(100vh - 150px)">
            <TabItems>
                <Syncfusion.Blazor.Navigations.TabItem>
                    <ChildContent>
                        <TabHeader Text="@($"My Worklist ({_worklist.Count})")"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <SfGrid DataSource="_worklist" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                            <GridEvents TValue="MemoDto" RowDataBound="OnRowDataBind"></GridEvents>
                            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn HeaderText="Code" AutoFit="true">
                                    <Template Context="kk">
                                        @{
                                            if (kk is MemoDto mm)
                                            {
                                                if (mm.LastAction == "Object" && mm.MemoCreatedByUserId.ToLower() == _userId.ToLower())
                                                {
                                                    @* @page "/memos/{StrMemoId}/edit/{StrApprovalLogId}" *@
                                                    var href2 = $"/memos/{mm.MemoId}/edit/{mm.MemoApprovalLogId}";
                                                    var target = $"mm-{mm.MemoId}";
                                                    <a href="@href2" target="@target">@mm.MemoCode</a>
                                                }
                                                else
                                                {
                                                    <a href="@($"/worklist/viewmemo/{mm.MemoApprovalLogId}")"
                                                       target="memo-@(mm.MemoId)"
                                                       id="@($"mm-{mm.MemoId}")"
                                                       class="mud-link"
                                                       style="color: #0d6efd; text-decoration: none; font-weight: bold;;">
                                                        @mm.MemoCode
                                                    </a>
                                                }
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Memo" Field="@nameof(MemoDto.MemoTitle)" AutoFit="true">

                                </GridColumn>
                                <GridColumn HeaderText="Type" Field="@nameof(MemoDto.MemoTyeShort)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated By" Field="@nameof(MemoDto.MemoCreatedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Initiated Date" Field="@nameof(MemoDto.MemoCreatedDate)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Pedning Since" Field="@nameof(MemoDto.PendingSince)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Forward By" Field="@nameof(MemoDto.ForwardedBy)" AutoFit="true"></GridColumn>
                                <GridColumn HeaderText="Last Action" AutoFit="true" Field="LastAction">
                                    @* <Template Context="context">
                                        @{
                                            if (context is MemoDto memo)
                                            {
                                                <div style="display:flex;gap:10px;">
                                                <span>@memo.LastAction</span>
                                                @if (memo.LastAction == "Object")
                                                {

                                                    <MudLink Href="@($"/memos/{memo.MemoId}/edit")" Style="font-size: 12px;">
                                                        Edit Memo
                                                    </MudLink>
                                                }
                                                </div>
                                            }
                                        }
                                    </Template> *@
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </ContentTemplate>
                </Syncfusion.Blazor.Navigations.TabItem>
                @if (_delegations.Any())
                {
                    <Syncfusion.Blazor.Navigations.TabItem>
                        <ChildContent>
                            <TabHeader Text="Delegations Assigned to Me"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <SfGrid DataSource="_delegations" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                                <GridColumns>
                                    <GridColumn HeaderText="From User" Field="@nameof(DelegationDto.FromUserName)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Type" Field="@nameof(DelegationDto.Type)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Date From" Field="@nameof(DelegationDto.DateFrom)" AutoFit="true" Format="dd/MM/yyyy"></GridColumn>
                                    <GridColumn HeaderText="Date To" Field="@nameof(DelegationDto.DateTo)" AutoFit="true" Format="dd/MM/yyyy"></GridColumn>
                                    <GridColumn HeaderText="Comments" Field="@nameof(DelegationDto.Comments)" AutoFit="true"></GridColumn>

                                </GridColumns>
                            </SfGrid>
                        </ContentTemplate>
                    </Syncfusion.Blazor.Navigations.TabItem>
                }
                @if (_delegatedWorklist.Any())
                {
                    <Syncfusion.Blazor.Navigations.TabItem>
                        <ChildContent>
                            <TabHeader Text="@($"Delegated Worklist ({_delegatedWorklist.Count})")"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <SfGrid DataSource="_delegatedWorklist" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" AllowTextWrap="true">
                                <GridEvents TValue="MemoDto" RowDataBound="OnRowDataBind"></GridEvents>
                                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                                <GridColumns>
                                    <GridColumn HeaderText="Code" AutoFit="true">
                                        <Template Context="kk">
                                            @{
                                                if (kk is MemoDto mm)
                                                {
                                                    <a href="@($"/worklist/viewmemo/{mm.MemoApprovalLogId}")"
                                                       target="memo-@(mm.MemoId)"
                                                       id="@($"mm-{mm.MemoId}")"
                                                       class="mud-link"
                                                       style="color: #0d6efd; text-decoration: none; font-weight: bold;;">
                                                        @mm.MemoCode
                                                    </a>
                                                }
                                            }
                                        </Template>
                                    </GridColumn>
                                    <GridColumn HeaderText="Memo" Field="@nameof(MemoDto.MemoTitle)" AutoFit="true">

                                    </GridColumn>
                                    <GridColumn HeaderText="Type" Field="@nameof(MemoDto.MemoTyeShort)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Initiated By" Field="@nameof(MemoDto.MemoCreatedBy)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Initiated Date" Field="@nameof(MemoDto.MemoCreatedDate)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Pedning Since" Field="@nameof(MemoDto.PendingSince)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Forward By" Field="@nameof(MemoDto.ForwardedBy)" AutoFit="true"></GridColumn>
                                    <GridColumn HeaderText="Last Action" AutoFit="true" Field="LastAction">
                                        @* <Template Context="context">
                                        @{
                                            if (context is MemoDto memo)
                                            {
                                                <div style="display:flex;gap:10px;">
                                                <span>@memo.LastAction</span>
                                                @if (memo.LastAction == "Object")
                                                {

                                                    <MudLink Href="@($"/memos/{memo.MemoId}/edit")" Style="font-size: 12px;">
                                                        Edit Memo
                                                    </MudLink>
                                                }
                                                </div>
                                            }
                                        }
                                    </Template> *@
                                    </GridColumn>
                                </GridColumns>
                            </SfGrid>
                        </ContentTemplate>
                    </Syncfusion.Blazor.Navigations.TabItem>
                }
            </TabItems>
        </SfTab>
    </div>
</div>

@code {

    // authorization
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string _userId = "";
    private List<MemoDto> _worklist = new();
    private List<DelegationDto> _delegations = new();
    private List<MemoDto> _delegatedWorklist = new();


    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        _worklist = await Service.GetMyWorkList(_userId);
        _delegations = await DelegationService.GetDelegationsAssignedToUser(_userId);

        // Get delegated worklist
        var delegatedFromUserIds = await DelegationService.GetDelegatedFromUserIds(_userId);
        _delegatedWorklist = await Service.GetDelegatedWorkList(delegatedFromUserIds);
    }

    public void ViewMemo(MemoDto memo)
    {
        NavMgr.NavigateTo($"/worklist/viewmemo/{memo.MemoApprovalLogId}");
    }

    private void OnRowDataBind(RowDataBoundEventArgs<MemoDto> obj)
    {
        if (obj.Data.LastAction == "Approved")
        {
            obj.Row.AddStyle(["background-color:#B7FFB7 "]);
        }
        else if (obj.Data.LastAction == "Object")
        {
            obj.Row.AddStyle(["background-color:#FF6699 "]);
        }
        else if (obj.Data.LastAction == "Reply")
        {
            obj.Row.AddStyle(["background-color:#ffd7c4 "]);
        }
    }

}
using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class MemoDto
{
    public byte? MemoStatusId { get; set; }

    public int MemoId { get; set; }

    public string? MemoCode { get; set; }
    [Required] public string? MemoTitle { get; set; }

    public string MemoStatus { get; set; } = string.Empty;

    public DateTime? MemoCreatedDate { get; set; }
    public string? PendingAt { get; set; } = string.Empty;
    public string? PendingSince { get; set; } = string.Empty;
    public string? ForwardedBy { get; set; } = string.Empty;
    public string? LastAction { get; set; } = string.Empty;
    public string? LastActionBy { get; set; } = string.Empty;
    public string? DelegatinApproval { get; set; } = string.Empty;
    public string? LastActionByDelegation { get; set; } = string.Empty;
    public DateTime? LastActionDate { get; set; }
    public string? RequiredApprovalFrom { get; set; } = string.Empty;

    public string MemoCreatedBy { get; set; } = string.Empty;

    public DateTime? MemoModifiedDate { get; set; }

    public string MemoModifiedBy { get; set; } = string.Empty;

    public bool MemoIsActive { get; set; }

    public bool MemoIsDel { get; set; }

    public int? MemoTemplateId { get; set; }

    public int? MemoTypeId { get; set; }

    public string? Department { get; set; }
    public string? Division { get; set; }




    public virtual ICollection<MemoApprover> MemoApprovers { get; set; } = new List<MemoApprover>();

    public virtual ICollection<MemoAttachment> MemoAttachments { get; set; } = new List<MemoAttachment>();

    public virtual ICollection<MemoSection> MemoSections { get; set; } = new List<MemoSection>();

    public virtual MemoType MemoType { get; set; }
    public string? MemoTypeStr { get; set; }
    public bool CanDelete { get; set; } = false;
    public string? MemoTyeShort { get; set; }
    public int MemoApprovalLogId { get; set; } = 0;
    public DateTime ActionDate { get; internal set; }
    public string? InitiatedBy { get; set; }
    public string? MemoCreatedByUserId { get; set; }
}
using System.ComponentModel.DataAnnotations;
using PSSmartMemo.Services;

namespace PSSmartMemo.DTO;

public class MemoSectionDto
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public int? MemoId { get; set; }
    public Guid? SectionId { get; set; }
    public string? Section { get; set; }
    /// <summary>
    /// Gets or sets the text content. Automatically handles encryption/decryption based on MemoIsEncrypted flag.
    /// </summary>
    public string? Content
    {
        get
        {
            if (_isEncrypted && !string.IsNullOrEmpty(_encryptedContent) && MemoId.HasValue)
            {
                try
                {
                    return EncryptionService.DecryptAndDecompress(_encryptedContent, MemoId.Value);
                }
                catch
                {
                    // If decryption fails, return the original content (backward compatibility)
                    return _encryptedContent;
                }
            }
            return _content;
        }
        set
        {
            _content = value;
            _encryptedContent = null; // Clear encrypted content when setting plain content
        }
    }
    public string? RefContent { get; set; }

    // Internal storage for encrypted content
    private string? _contentHtml;
    private string? _encryptedContentHtml;
    private string? _content;
    private string? _encryptedContent;
    private bool _isEncrypted;

    /// <summary>
    /// Gets or sets the HTML content. Automatically handles encryption/decryption based on MemoIsEncrypted flag.
    /// </summary>
    public string? ContentHtml
    {
        get
        {
            if (_isEncrypted && !string.IsNullOrEmpty(_encryptedContentHtml) && MemoId.HasValue)
            {
                try
                {
                    return EncryptionService.DecryptAndDecompress(_encryptedContentHtml, MemoId.Value);
                }
                catch
                {
                    // If decryption fails, return the original content (backward compatibility)
                    return _encryptedContentHtml;
                }
            }
            return _contentHtml;
        }
        set
        {
            _contentHtml = value;
            _encryptedContentHtml = null; // Clear encrypted content when setting plain content
        }
    }

    /// <summary>
    /// Sets the encrypted content directly from database
    /// </summary>
    public void SetEncryptedContent(string? encryptedContentHtml, bool isEncrypted)
    {
        _encryptedContentHtml = encryptedContentHtml;
        _isEncrypted = isEncrypted;
        _contentHtml = null; // Clear plain content when setting encrypted content
    }

    /// <summary>
    /// Sets the encrypted text content directly from database
    /// </summary>
    public void SetEncryptedTextContent(string? encryptedContent, bool isEncrypted)
    {
        _encryptedContent = encryptedContent;
        _isEncrypted = isEncrypted;
        _content = null; // Clear plain content when setting encrypted content
    }

    /// <summary>
    /// Gets the HTML content ready for database storage (encrypted if MemoIsEncrypted is true)
    /// </summary>
    public string? GetContentForStorage()
    {
        if (MemoIsEncrypted && !string.IsNullOrEmpty(_contentHtml) && MemoId.HasValue)
        {
            try
            {
                return EncryptionService.EncryptAndCompress(_contentHtml, MemoId.Value);
            }
            catch
            {
                // If encryption fails, store as plain text (fallback)
                return _contentHtml;
            }
        }
        return _contentHtml;
    }

    /// <summary>
    /// Gets the text content ready for database storage (encrypted if MemoIsEncrypted is true)
    /// </summary>
    public string? GetTextContentForStorage()
    {
        if (MemoIsEncrypted && !string.IsNullOrEmpty(_content) && MemoId.HasValue)
        {
            try
            {
                return EncryptionService.EncryptAndCompress(_content, MemoId.Value);
            }
            catch
            {
                // If encryption fails, store as plain text (fallback)
                return _content;
            }
        }
        return _content;
    }

    public bool MemoIsActive {  get; set; }
    public Guid MemoTemplateSectionId { get; set; }
    public string? TemplateSectionTitle {  get; set; }
    public string? TemplateContentHtml { get; set; }
    public bool IsRequired { get; set; }
    public int? SectionSortOrder { get; set; } = 0;
    public bool isOpen { get; set; } = false;
    public bool IgnoreSection { get; set; } = false;
    public string? Placeholder { get; set; }
    public bool IsWaterMark { get; set; } = true;

    /// <summary>
    /// Indicates whether this memo section should be encrypted
    /// </summary>
    public bool MemoIsEncrypted { get; set; } = true; // Default to true for new memos
}
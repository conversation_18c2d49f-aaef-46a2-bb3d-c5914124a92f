using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class TemplateSectionDto
{
    public Guid TemplateSectionId { get; set; } = Guid.NewGuid();
    [Required]
    public string? SectionTitle { get; set; }
    [Required]
    public string? ContentHtml { get; set; }

    public bool IsRequired { get; set; }
    public int? SectionSortOrder { get; set; } = 0;
    public bool isOpen { get; set; } = false;
    public bool IsWaterMark { get; set; } = false;
    public bool CanDelete { get; set; } = true;
}

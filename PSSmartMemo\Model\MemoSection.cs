﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoSection
{
    public Guid MemoSectionId { get; set; }

    public string MemoSectionCode { get; set; }

    public string MemoSectionTitle { get; set; }

    public string MemoSectionContentText { get; set; }

    public string MemoSectionContentHtml { get; set; }

    public int? MemoSectionSortOrder { get; set; }

    public bool MemoSectionIsActive { get; set; }

    public bool MemoSectionIsDel { get; set; }

    public DateTime? MemoSectionCreatedDate { get; set; }

    public string MemoSectionCreatedBy { get; set; }

    public DateTime? MemoSectionModifiedDate { get; set; }

    public string MemoSectionModifiedBy { get; set; }

    public int? MemoId { get; set; }

    public Guid MemoTemplateSectionId { get; set; }

    public bool MemoSectionIgnored { get; set; }

    public bool MemoIsEncrypted { get; set; }

    public virtual Memo Memo { get; set; }

    public virtual MemoTemplateSection MemoTemplateSection { get; set; }
}
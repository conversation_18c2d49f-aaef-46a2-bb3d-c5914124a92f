﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoTemplate
{
    public int MemoTemplateId { get; set; }

    public string MemoTemplateCode { get; set; }

    public string MemoTemplatePrefixCode { get; set; }

    public string MemoTemplateTitle { get; set; }

    public bool MemoTemplateAttachmentAllowed { get; set; }

    public double? MemoTemplateAttachmentPerFileSizeMballowed { get; set; }

    public decimal? MemoTemplateAttachmentFileCountAllowed { get; set; }

    public int? MemoTemplateApproverCountAllowed { get; set; }

    public string MemoTemplateVersion { get; set; }

    public bool MemoTemplateIsActive { get; set; }

    public bool MemoTemplateIsDel { get; set; }

    public DateTime? MemoTemplateCreatedDate { get; set; }

    public string MemoTemplateCreatedBy { get; set; }

    public DateTime? MemoTemplateModifiedDate { get; set; }

    public string MemoTemplateModifiedBy { get; set; }

    public int? MemoTypeId { get; set; }

    public int? FormTypeId { get; set; }

    public byte MemoTemplateStatus { get; set; }

    public virtual FormType FormType { get; set; }

    public virtual ICollection<MemoTemplateApprover> MemoTemplateApprovers { get; set; } = new List<MemoTemplateApprover>();

    public virtual ICollection<MemoTemplateSection> MemoTemplateSections { get; set; } = new List<MemoTemplateSection>();

    public virtual ICollection<MemoTemplateUserRoleAssign> MemoTemplateUserRoleAssigns { get; set; } = new List<MemoTemplateUserRoleAssign>();

    public virtual MemoType MemoType { get; set; }

    public virtual ICollection<Memo> Memos { get; set; } = new List<Memo>();

    public virtual ICollection<ReportUsersMemoTemplate> ReportUsersMemoTemplates { get; set; } = new List<ReportUsersMemoTemplate>();
}
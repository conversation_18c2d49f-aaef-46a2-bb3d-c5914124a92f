using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.EntityFrameworkCore;
using MudBlazor.Services;
using OfficeOpenXml;
using PSSmartMemo.Components;
using Smart.Blazor;
using Syncfusion.Licensing;

var builder = WebApplication.CreateBuilder(args);

// configure non commercial license of epplus
ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();

builder.Services.AddAuthorization(options => { options.FallbackPolicy = options.DefaultPolicy; });
builder.Services.AddSignalR(option => { option.MaximumReceiveMessageSize = 1000 * 1024; });

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddMudServices();
SyncfusionLicenseProvider.RegisterLicense(
    "MzIzODczMkAzMjM1MmUzMDJlMzBQVFVDcXQ3dW8yQnU3YlArTk5SZU1GVGFxTlRpUXU5WlZubXFtckUvMkx3PQ==");

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContextFactory<ApplicationDbContext>(options => 
    options.UseSqlServer(connectionString));

builder.Services.AddServerSideBlazor().AddCircuitOptions(options => { options.DetailedErrors = true; });

builder.Services.AddScoped<SfDialogService>();
builder.Services.AddScoped<AdminDataService>();
builder.Services.AddScoped<AppDataService>();
builder.Services.AddScoped<CorporateService>();
builder.Services.AddScoped<LocationDataService>();
builder.Services.AddScoped<FormTypeDataService>();
builder.Services.AddScoped<MemoTypeDataService>();
builder.Services.AddScoped<TemplateDataService>();
builder.Services.AddScoped<MemoDataService>();
builder.Services.AddScoped<ApprovalRoleDataService>();
builder.Services.AddScoped<WorklistDataService>();
builder.Services.AddScoped<WatchListDataService>();
builder.Services.AddScoped<AttachmentTypeDataService>();
builder.Services.AddScoped<CorpApprovalDataService>();
builder.Services.AddScoped<DelegationDataService>();
builder.Services.AddScoped<ReportUserMemoTemplateDataService>();
builder.Services.AddScoped<ReportMemoDataService>();

//builder.Services.AddScoped<EmployeeDataService>();

builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddSyncfusionBlazor();
builder.Services.AddSmart();
builder.Services.AddHostedService<TempFileCleanupService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();

﻿using System.Reflection;

namespace PSSmartMemo.Services;

public class AdminDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    //public Task<List<SectionDto>> getSections()
    //{
    //    var q = (from s in dc.Sections
    //        orderby s.Module.ModuleTitle, s.SectionTitle
    //             //where s.ModuleId == null
    //        //where s.ModuleId == null
    //        select new SectionDto
    //        {
    //            IsActive = s.IsActive ?? false,
    //            Name = s.SectionTitle ?? "",
    //            Id = s.SectionId,
    //            Description = s.SectionDescription ?? "",
    //            ModuleId = s.ModuleId,
    //            SortOrder = s.SortOrder,
    //            PostTypeId = s.PostTypeId,
    //            Posts = new List<PostDTO>(), 
    //            Module = s.Module.ModuleTitle ?? "Main", 
    //            PostType=s.PostType.PostTypeTitle, isSelect=false
    //        }).ToList();

    //    return Task.FromResult(q);
    //}

    public Task<List<UserDTO>> GetAllAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Users
                 orderby x.Name
                 select new UserDTO
                 {
                     IsActive = x.IsActive,
                     Note = x.Note ?? "",
                     Name = x.Name,
                     Code = string.Format("{0:000}", x.Id), //x.Code,
                     Email = x.Email.ToLower(),
                     UserId = x.UserId,
                     //Pin = x.Pin,
                     Mobile = x.MobileNumber,
                     IsAdmin = x.IsAdmin,
                     EmployeeCode = x.EmployeeCode,
                     Id = x.Id
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<RoleDTO>> GetAllRolesAsyncWithSelectedUser(int uId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Roles
                 where x.IsActive == true
                 orderby x.Name
                 select new RoleDTO
                 {
                     IsActive = x.IsActive,
                     Notes = x.Notes ?? "",
                     Name = x.Name,
                     Code = string.Format("{0:000}", x.Id), // x.Code,
                     Id = x.Id,
                     isSelect = false
                 }).ToList();

        List<RoleDTO> g = (from x in dc.Roles
                           join ur in dc.UserRoles on x.Id equals ur.RoleId
                           join u in dc.Users on ur.UserId equals u.Id
                           where u.Id == uId
                           select new RoleDTO
                           {
                               Id = x.Id
                           }).ToList();

        foreach (var item in q)
        {
            var role = g.FirstOrDefault(mm => mm.Id == item.Id);
            item.isSelect = role != null;
        }

        return Task.FromResult(q);
    }

    public Task<UserDTO> GetAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Users
                 where x.Id == id
                 select new UserDTO
                 {
                     IsActive = x.IsActive,
                     Note = x.Note ?? "",
                     Name = x.Name,
                     Code = $"{x.Id:000}", //x.Code,
                     Email = x.Email,
                     //Pin = x.Pin,
                     Mobile = x.MobileNumber,
                     IsAdmin = x.IsAdmin,
                     Password = x.Password,
                     ConfirmPassword = x.Password,
                     //ConfirmPin = x.Pin,
                     UserId = x.UserId,
                     Id = x.Id,
                     EmployeeCode = x.EmployeeCode
                 }).First();
        return Task.FromResult(q);
    }

    public Task<string> saveUserRolesInUserScreen(List<RoleDTO> roles, int uId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.UserRoles.Where(ur => ur.UserId == uId).ToList();
        dc.UserRoles.RemoveRange(q);
        dc.SaveChanges();

        var saveObj = roles.Where(r => r.isSelect).ToList();
        foreach (var item in saveObj)
        {
            var ur = new UserRole
            {
                UserId = uId,
                RoleId = item.Id,
                Notes = "",
                IsActive = true,
                CreatedBy = 1,
                CreatedDate = DateTime.Now
            };
            dc.UserRoles.Add(ur);
            dc.SaveChanges();
        }

        return Task.FromResult("Ok");
    }

    public Task<string> IsValid(UserDTO obj)
    {
        var dc = contextFactory.CreateDbContext();
        obj.Email = (obj.Email ?? "").Trim();
        obj.Name = (obj.Name ?? "").Trim();
        obj.UserId = (obj.UserId ?? "").Trim();
        obj.EmployeeCode = (obj.EmployeeCode ?? "").Trim();
        obj.Mobile = (obj.Mobile ?? "").Trim();


        // Check duplicate name
        var exist = (from aa in dc.Users
                     where aa.Id != obj.Id &&
                           aa.Email == obj.Email
                     select aa.Name).Any();
        if (exist) return Task.FromResult("Email already exist");

        obj.UserId = obj.UserId.Trim().ToLower();
        exist = (from aa in dc.Users
                 where aa.Id != obj.Id &&
                       aa.UserId == obj.UserId
                 select aa).Any();

        if (exist) return Task.FromResult("User ID already exist");

        // trim code
        obj.Code = (obj.Code ?? "").Trim();

        if (string.IsNullOrEmpty(obj.EmployeeCode)) return Task.FromResult("Employee Code is required");


        // if code already assigned to another user
        exist = (from aa in dc.Users
                 where aa.Id != obj.Id &&
                       aa.EmployeeCode == obj.EmployeeCode
                 select aa).Any();

        if (exist) return Task.FromResult("Employee Code already assigned to another user");

        return Task.FromResult("OK");
    }

    public Task<UserDTO> SaveAsync(UserDTO obj, int userId)
    {
        var dc = contextFactory.CreateDbContext();
        var st = dc.Users.Find(obj.Id);
        if (st == null)
        {
            st = new User
            {
                Note = obj.Note ?? "",
                Id = 0,
                Name = obj.Name ?? "",
                MobileNumber = obj.Mobile ?? "",
                Email = (obj.Email ?? "").ToLower(),
                Password = obj.Password ?? "",
                IsAdmin = obj.IsAdmin,
                //Pin = obj.Pin ?? "",
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                IsActive = obj.IsActive,
                Code = obj.Code ?? "",
                UserId = obj.UserId.Trim().ToLower(),
                EmployeeCode = obj.EmployeeCode
            };
            dc.Users.Add(st);
            dc.SaveChanges();
            st.Code = $"{st.Id:000}";
            dc.SaveChanges();
            obj.Id = st.Id;
        }
        else
        {
            st.Note = obj.Note;
            st.Name = obj.Name ?? "";
            st.ModifiedBy = userId;
            st.ModifiedDate = DateTime.Now;
            st.IsActive = obj.IsActive;
            st.IsAdmin = obj.IsAdmin;
            //st.Pin = obj.Pin ?? "";
            st.Code = $"{st.Id:000}"; //obj.Code ?? "";
            st.MobileNumber = obj.Mobile ?? "";
            st.Email = (obj.Email ?? "").Trim().ToLower();
            st.UserId = obj.UserId.ToLower().Trim();
            st.EmployeeCode = obj.EmployeeCode;
            //st.Password = obj.Password ?? "";
            dc.SaveChanges();
            obj.Id = st.Id;
        }

        return Task.FromResult(obj);
    }

    public Task<string> DeleteAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var obj = dc.Users.FirstOrDefault(x => x.Id == id);
        if (obj == null) return Task.FromResult("Record not found");
        try
        {
            dc.Users.Remove(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<MenuDto>> GetAllMenusAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Menus
                 orderby x.MenuParentId, x.Name
                 select new MenuDto
                 {
                     IsActive = x.IsActive,
                     Name = x.Name,
                     Code = x.Code,
                     Url = x.Url,
                     Device = "", //x.Device,
                     Id = x.Id,
                     ModuleName = dc.Modules.SingleOrDefault(m => m.Id == x.ModuleId)!.ModuleTitle.ToString(),
                     ParentId = x.MenuParentId == 0 ? null : x.MenuParentId,
                     ParentMenu = x.MenuParentId == 0
                         ? ""
                         : dc.Menus.SingleOrDefault(m => m.Id == x.MenuParentId)!.Name.ToString(),
                     SortOrder = x.SortOrder,
                     Icon = x.Icon
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<MenuDto>> GetAllActiveMenusAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Menus
                 orderby x.MenuParentId, x.Name
                 where x.IsActive == true
                 select new MenuDto
                 {
                     IsActive = x.IsActive,
                     Name = x.Name,
                     Code = x.Code,
                     Url = x.Url,
                     Device = "", //x.Device,
                     Id = x.Id,
                     ModuleName = dc.Modules.SingleOrDefault(m => m.Id == x.ModuleId)!.ModuleTitle.ToString(),
                     ParentId = x.MenuParentId == 0 ? null : x.MenuParentId,
                     ParentMenu = x.MenuParentId == 0
                         ? ""
                         : dc.Menus.SingleOrDefault(m => m.Id == x.MenuParentId)!.Name.ToString(),
                     SortOrder = x.SortOrder,
                     Icon = x.Icon
                 }).ToList();
        foreach (var item in q)
        {
            var pm = dc.Menus.FirstOrDefault(m => m.Id == item.ParentId);
            if (pm != null)
                item.Name = pm.Name + " - " + item.Name;
        }

        // order by name
        q = q.OrderBy(m => m.Name).ToList();
        q.Add(new MenuDto { Id = 0, Name = "-----None-----" });

        return Task.FromResult(q);
    }

    public Task<List<Modules>> getAllModules()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from m in dc.Modules
                 where m.IsActive == true
                 select new Modules { ID = m.Id, ModuleTitle = m.ModuleTitle }).ToList();
        return Task.FromResult(q);
    }


    public Task<List<string>> GetIconsList()
    {
        var dc = contextFactory.CreateDbContext();
        var fields =
            typeof(Icons.Material.Filled).GetFields(BindingFlags.Public | BindingFlags.Static |
                                                    BindingFlags.FlattenHierarchy);
        var icons = new List<string>();
        foreach (var field in fields)
        {
            var iconName = field.Name;
            icons.Add(iconName);
            //string iconValue = (string)field.GetValue(null);

            //Console.WriteLine($"Icon Name: {iconName}, Icon Value: {iconValue}");
        }

        return Task.FromResult(icons);
    }

    public Task<string> IsMenuValid(MenuDto obj)
    {
        var dc = contextFactory.CreateDbContext();
        //obj.Name = obj.Name!.Trim();

        // Check duplicate code
        //var exist = (from aa in dc.Users
        //             where aa.Id != obj.Id &&
        //                     aa.Name == obj.Name
        //             select aa.Name).Any();
        //if (exist)
        //{
        //    return Task.FromResult("Name already exist");
        //}

        // Check duplicate name
        //var exist = (from aa in dc.Roles
        //             where aa.Id != obj.Id &&
        //                   aa.Name == obj.Name
        //             select aa.Name).Any();
        //if (exist)
        //{
        //    return Task.FromResult("Name already exist");
        //}

        return Task.FromResult("OK");
    }

    public Task<MenuDto> SaveMenuAsync(MenuDto obj, int userId)
    {
        var dc = contextFactory.CreateDbContext();
        var st1 = dc.Menus.Any(x => x.Id == obj.Id);
        if (st1 == false)
        {
            var st = new Menu
            {
                Id = 0,
                Name = obj.Name ?? "",
                Url = obj.Url ?? "",
                Urltarget = obj.URLTarget ?? "",
                Device = "",
                ModuleId = obj.ModuleId!.Value,
                MenuParentId = obj.ParentId,
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                IsActive = obj.IsActive,
                Code = obj.Code ?? "",
                SortOrder = obj.SortOrder,
                Icon = obj.Icon
            };
            dc.Menus.Add(st);
            dc.SaveChanges();
            var st2 = dc.Menus.First(m => m.Id == st.Id);
            st2.Code = $"{st.Id:000}";
            dc.SaveChanges();
            obj.Id = st.Id;
        }
        else
        {
            var st = dc.Menus.SingleOrDefault(x => x.Id == obj.Id)!;
            st.Name = obj.Name ?? "";
            st.Url = obj.Url ?? "";
            st.Urltarget = obj.URLTarget ?? "";
            st.Device = "";
            st.ModuleId = obj.ModuleId!.Value;
            st.MenuParentId = obj.ParentId;
            st.ModifiedBy = userId;
            st.ModifiedDate = DateTime.Now;
            st.IsActive = obj.IsActive;
            st.Code = $"{obj.Id:000}" /*obj.Code ?? ""*/;
            st.SortOrder = obj.SortOrder;
            st.Icon = obj.Icon;
            obj.Id = st.Id;
            dc.SaveChanges();
        }

        return Task.FromResult(obj);
    }

    public Task<MenuDto> GetMenuAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Menus
                 where x.Id == id
                 select new MenuDto
                 {
                     IsActive = x.IsActive,
                     Name = x.Name,
                     Code = $"{x.Id:000}", //x.Code,
                     ModuleId = x.ModuleId,
                     Url = x.Url ?? "",
                     URLTarget = x.Urltarget ?? "",
                     ParentId = x.MenuParentId,
                     Device = x.Device,
                     Id = x.Id,
                     SortOrder = x.SortOrder,
                     Icon = x.Icon
                 }).First();
        return Task.FromResult(q);
    }

    public Task<string> DeleteMenusAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var obj = dc.Menus.FirstOrDefault(x => x.Id == id);
        if (obj == null) return Task.FromResult("Record not found");
        try
        {
            dc.Menus.Remove(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> SaveUserRoleAsync(int selUserId, int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        var ur = new UserRole
        {
            UserId = selUserId,
            RoleId = roleId,
            Notes = "",
            IsActive = true,
            CreatedBy = 1,
            CreatedDate = DateTime.Now
        };
        dc.UserRoles.Add(ur);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<string> DeleteUserRoleAsync(int selUserId, int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        var obj = dc.UserRoles.FirstOrDefault(x => x.UserId == selUserId && x.RoleId == roleId);
        if (obj == null) return Task.FromResult("Record not found");
        try
        {
            dc.UserRoles.Remove(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public static string EmpNoUpdate(string empNo)
    {
        
        if (string.IsNullOrEmpty(empNo))
            return "";
        if (empNo == @"KHI-SOFT-056\Jawaid" || empNo == @"NABIL-PC\Administrator") return empNo;
        empNo = empNo.Replace("PSMCL\\PKB0", "");

        if (long.Parse(empNo) >= 1 && long.Parse(empNo) <= 1000)
        {
            if (empNo.Length == 1) empNo = "0010000" + empNo;
            else if (empNo.Length == 2) empNo = "001000" + empNo;
            else if (empNo.Length == 3) empNo = "00100" + empNo;
            else if (empNo.Length == 4) empNo = "0010" + empNo;
            else if (empNo.Length == 5) empNo = "001" + empNo;
        }
        else if (long.Parse(empNo) >= 20001 && long.Parse(empNo) <= 21000)
        {
            empNo = "001" + empNo;
        }
        else if (long.Parse(empNo) >= 21001 && long.Parse(empNo) <= 29999)
        {
            empNo = "005" + empNo;
        }
        else if (long.Parse(empNo) >= 30001 && long.Parse(empNo) <= 39999)
        {
            empNo = "001" + empNo;
        }
        else if (long.Parse(empNo) >= 50001 && long.Parse(empNo) <= 69999)
        {
            empNo = "003" + empNo;
        }
        else if (long.Parse(empNo) >= 70001 && long.Parse(empNo) <= 79999)
        {
            empNo = "003" + empNo;
        }
        else if (long.Parse(empNo) >= 80001 && long.Parse(empNo) <= 89999)
        {
            empNo = "004" + empNo;
        }
        else if (long.Parse(empNo) >= 95001 && long.Parse(empNo) <= 99999)
        {
            empNo = "006" + empNo;
        }
        else if (long.Parse(empNo) >= 96999 && long.Parse(empNo) <= 99999)
        {
            empNo = "004" + empNo;
        }
        else if (long.Parse(empNo) >= 1001 && long.Parse(empNo) <= 9999)
        {
            empNo = "008" + empNo;
        }
        else if (long.Parse(empNo) >= 10001 && long.Parse(empNo) <= 19999)
        {
            empNo = "008" + empNo;
        }

        return empNo;
    }

    public Task<List<UserDTO>> GetAllActiveUsers()
    {
        var dc = contextFactory.CreateDbContext();
        var q2 = (from x in dc.MemoApprovalLogs
            where x.ReplyLogId == null
            select x.ToApprover.MemoApproverUserId).Distinct().ToList();
        var q = (from x in dc.Users
            where x.IsActive == true &&
                  q2.Contains(x.UserId)
            orderby x.Name
            select new UserDTO
            {
                Name = $"{x.UserId} - {x.Name}",
                UserId = x.UserId
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<UserDTO>> GetAllUsers()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Users
                 where x.IsActive == true
                 orderby x.Name
                 select new UserDTO
                 {
                     Name = $"{x.UserId} - {x.Name}",
                     UserId = x.UserId
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<UserDTO>> GetAllActiveAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Users
                 where x.IsActive == true
                 orderby x.Name
                 select new UserDTO
                 {
                     IsActive = x.IsActive,
                     Note = x.Note ?? "",
                     Name = x.Name,
                     Code = $"{x.Id:000}" /*x.Code*/,
                     Email = x.Email.ToLower(),
                     //Pin = x.Pin,
                     Mobile = x.MobileNumber,
                     IsAdmin = x.IsAdmin,
                     EmployeeCode = EmpNoUpdate(x.EmployeeCode),
                     Id = x.Id,
                     UserId = x.UserId.Replace("PSMCL\\", "").ToUpper()
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<UserDTO>> GetUserRoleAsync(int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Users
                 join ur in dc.UserRoles on x.Id equals ur.UserId
                 where ur.RoleId == roleId && x.IsActive == true
                 select new UserDTO
                 {
                     Name = x.Name,
                     Email = x.Email.ToLower(),
                     Id = x.Id,
                     UserId = x.UserId.Replace("psmcl\\", "").ToUpper(),
                     EmployeeCode = EmpNoUpdate(x.EmployeeCode)
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> SaveRoleMenuAsync(int selMenuId, int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        // if role menu already exists then ignore otherwise add it
        var isExist = dc.RoleMenus.Any(m => m.RoleId == roleId && m.MenuId == selMenuId);
        if (!isExist)
        {
            var ur = new RoleMenu
            {
                MenuId = selMenuId,
                RoleId = roleId,
                IsActive = true,
                CreatedBy = 1,
                CreatedDate = DateTime.Now
            };
            dc.RoleMenus.Add(ur);
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<string> DeleteRoleMenuAsync(int selMenuId, int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        var obj = dc.RoleMenus.FirstOrDefault(x => x.MenuId == selMenuId && x.RoleId == roleId);
        if (obj == null) return Task.FromResult("Record not found");
        try
        {
            dc.RoleMenus.Remove(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<MenuDto>> GetAllActiveMenuAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Menus
                 where x.IsActive == true
                 orderby x.SortOrder
                 select new MenuDto
                 {
                     Name = x.Name,
                     Code = $"{x.Id:000}", // x.Code,
                     Device = x.Device,
                     Id = x.Id,
                     ModuleName = ""
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<MenuDto>> GetRoleMenuAsync(int roleId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Menus
                 join ur in dc.RoleMenus on x.Id equals ur.MenuId
                 where ur.RoleId == roleId && x.IsActive == true
                 orderby x.SortOrder
                 select new MenuDto
                 {
                     IsActive = x.IsActive,
                     Name = x.Name,
                     Code = x.Code,
                     Url = x.Url,
                     Device = "", //x.Device,
                     Id = x.Id,
                     ModuleName = dc.Modules.SingleOrDefault(m => m.Id == x.ModuleId)!.ModuleTitle.ToString(),
                     ParentId = x.MenuParentId == 0 ? null : x.MenuParentId,
                     ParentMenu = x.MenuParentId == 0
                         ? ""
                         : dc.Menus.SingleOrDefault(m => m.Id == x.MenuParentId)!.Name.ToString()
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<RoleDTO>> GetAllRolesAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Roles
                 orderby x.Name
                 select new RoleDTO
                 {
                     IsActive = x.IsActive,
                     Notes = x.Notes ?? "",
                     Name = x.Name,
                     Code = $"{x.Id:000}", // x.Code,
                     Id = x.Id
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> IsRoleValid(RoleDTO obj)
    {
        // obj.Name = obj.Name!.Trim();

        // Check duplicate code
        //var exist = (from aa in dc.Users
        //             where aa.Id != obj.Id &&
        //                     aa.Name == obj.Name
        //             select aa.Name).Any();
        //if (exist)
        //{
        //    return Task.FromResult("Name already exist");
        //}

        // Check duplicate name
        //var exist = (from aa in dc.Roles
        //             where aa.Id != obj.Id &&
        //                   aa.Name == obj.Name
        //             select aa.Name).Any();
        //if (exist)
        //{
        //    return Task.FromResult("Name already exist");
        //}

        return Task.FromResult("OK");
    }

    public Task<RoleDTO> SaveRoleAsync(RoleDTO obj, int userId)
    {
        var dc = contextFactory.CreateDbContext();
        var st = dc.Roles.Find(obj.Id);
        if (st == null)
        {
            st = new Role
            {
                Notes = obj.Notes ?? "",
                Id = 0,
                Name = obj.Name ?? "",
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                IsActive = obj.IsActive,
                Code = $"{obj.Id:000}" //obj.Code ?? ""
            };
            dc.Roles.Add(st);
            dc.SaveChanges();
            st.Code = $"{st.Id:000}";
            dc.SaveChanges();
            obj.Id = st.Id;
        }
        else
        {
            st.Notes = obj.Notes ?? "";
            st.Name = obj.Name ?? "";
            st.ModifiedBy = userId;
            st.ModifiedDate = DateTime.Now;
            st.IsActive = obj.IsActive;
            st.Code = $"{st.Id:000}"; // obj.Code ?? "";
            dc.SaveChanges();
            obj.Id = st.Id;
        }

        return Task.FromResult(obj);
    }

    public Task<RoleDTO?> GetRoleAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from x in dc.Roles
                 where x.Id == id
                 select new RoleDTO
                 {
                     IsActive = x.IsActive,
                     Notes = x.Notes ?? "",
                     Name = x.Name,
                     Code = $"{x.Id:000}", //x.Code,
                     Id = x.Id
                 }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<string> DeleteRoleAsync(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var obj = dc.Roles.FirstOrDefault(x => x.Id == id);
        if (obj == null) return Task.FromResult("Record not found");
        try
        {
            dc.Roles.Remove(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    //public async Task<SectionDto> getSectionWithId(int sectionId)
    //{
    //    //var q = dc.Sections.SingleOrDefault(x => x.SectionId == sectionId);
    //    //return await Task.FromResult(q);
    //    var q = (from s in dc.Sections
    //             where s.SectionId == sectionId
    //             select new SectionDto
    //             {
    //                 IsActive = s.IsActive ?? false,
    //                 Name = s.SectionTitle,
    //                 Id = s.SectionId,
    //                 Description = s.SectionDescription,
    //                 ModuleId = s.ModuleId,
    //                 SortOrder = s.SortOrder,
    //                 PostTypeId = s.PostTypeId
    //             }).FirstOrDefault();
    //    return await Task.FromResult(q);
    //}

    //public Task<List<SectionDto>> SectionAsyncWithSelectedRole(int roleId)
    //{
    //    var q = (from x in dc.Sections
    //        where x.IsActive == true
    //        orderby x.SectionTitle
    //        select new SectionDto
    //        {
    //            Name = x.SectionTitle,
    //            Id = x.SectionId,
    //            isSelect = false
    //        }).ToList();

    //    List<SectionDto> g = (from x in dc.Sections
    //        join ur in dc.SectionRoles on x.SectionId equals ur.SectionId
    //        join u in dc.Sections on ur.SectionId equals u.SectionId
    //        where ur.RoleId == roleId
    //        select new SectionDto
    //        {
    //            Id = x.SectionId
    //        }).ToList();

    //    foreach (var item in q)
    //    {
    //        var section = g.FirstOrDefault(g => g.Id == item.Id);
    //        item.isSelect = section != null;
    //    }

    //    return Task.FromResult(q);
    //}
    //public Task<List<SectionDto>> GetAllSectionAsyncWithSelectedRole(int roleId)
    //{
    //    var q = (from x in dc.Sections
    //             where x.IsActive == true
    //             orderby x.SectionTitle
    //             select new SectionDto
    //             {
    //                 Name = x.SectionTitle,
    //                 Id = x.SectionId,
    //                 isSelect = false
    //             }).ToList();

    //    List<SectionDto> g = (from x in dc.Sections
    //                          join ur in dc.SectionRoles on x.SectionId equals ur.SectionId
    //                          join u in dc.Sections on ur.SectionId equals u.SectionId
    //                          where ur.RoleId == roleId
    //                          select new SectionDto
    //                          {
    //                              Id = x.SectionId,
    //                          }).ToList();

    //    foreach (var item in q)
    //    {
    //        var section = g.FirstOrDefault(g => g.Id == item.Id);
    //        if (section != null) item.isSelect = true; else item.isSelect = false;
    //    }

    //    return Task.FromResult(q);
    //}
    //public Task<List<RoleDTO>> GetAllRolesAsyncWithSelectedSection(int sectionId)
    //{
    //    var q = (from x in dc.Roles
    //        where x.IsActive == true
    //        orderby x.Name
    //        select new RoleDTO
    //        {
    //            IsActive = x.IsActive,
    //            Notes = x.Notes ?? "",
    //            Name = x.Name,
    //            Code = $"{x.Id:000}", // x.Code,
    //            Id = x.Id,
    //            isSelect = false
    //        }).ToList();

    //    List<RoleDTO> g = (from x in dc.Roles
    //        join ur in dc.SectionRoles on x.Id equals ur.RoleId
    //        join u in dc.Sections on ur.SectionId equals u.SectionId
    //        where u.SectionId == sectionId
    //        select new RoleDTO
    //        {
    //            Id = x.Id
    //        }).ToList();

    //    foreach (var item in q)
    //    {
    //        var role = g.FirstOrDefault(g => g.Id == item.Id);
    //        if (role != null) item.isSelect = true;
    //        else item.isSelect = false;
    //    }

    //    return Task.FromResult(q);
    //}

    //public Task<string> savePostRolesInSectionScreen(List<RoleDTO> roles, int sectionId)
    //{
    //    var q = dc.SectionRoles.Where(ur => ur.SectionId == sectionId).ToList();
    //    dc.SectionRoles.RemoveRange(q);
    //    dc.SaveChanges();

    //    var saveObj = roles.Where(r => r.isSelect).ToList();
    //    foreach (var item in saveObj)
    //    {
    //        var ur = new SectionRole
    //        {
    //            SectionId = sectionId,
    //            RoleId = item.Id
    //        };
    //        dc.SectionRoles.Add(ur);
    //        dc.SaveChanges();
    //    }

    //    return Task.FromResult("Ok");
    //}

    //public Task<string> savePostSectionInRoleScreen(List<SectionDto> sections, int roleId)
    //{
    //    var q = dc.SectionRoles.Where(ur => ur.RoleId == roleId).ToList();
    //    dc.SectionRoles.RemoveRange(q);
    //    dc.SaveChanges();

    //    var saveObj = sections.Where(r => r.isSelect).ToList();
    //    foreach (var item in saveObj)
    //    {
    //        var ur = new SectionRole
    //        {
    //            SectionId = item.Id,
    //            RoleId = roleId
    //        };
    //        dc.SectionRoles.Add(ur);
    //        dc.SaveChanges();
    //    }

    //    return Task.FromResult("Ok");
    //}

    //public async Task showHideSection(int sectionId)
    //{
    //    var q = dc.Sections.SingleOrDefault(x => x.SectionId == sectionId);
    //    if (q != null) q.IsActive = q.IsActive != true;
    //    await dc.SaveChangesAsync();
    //}

    public Task<List<AttachmentTypeDto>> GetAttachmentTypes()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.AttachmentTypes
                 orderby a.AttachmentTypeTitle
                 select new AttachmentTypeDto
                 {
                     Id = a.AttachmentTypeId,
                     Title = a.AttachmentTypeTitle
                 }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<AttachmentTypeDto>> GetAttachmentTypes(int memoTemplateId)
    {
        var dc = contextFactory.CreateDbContext();
        var temp = dc.MemoTemplates.First(c => c.MemoTemplateId == memoTemplateId);
        var memoTypeId = temp.MemoTypeId;
        var q = (from a in dc.AttachmentTypes
                 where a.MemoTypeId == null || a.MemoTypeId == memoTypeId
                 orderby a.AttachmentTypeTitle
                 select new AttachmentTypeDto
                 {
                     Id = a.AttachmentTypeId,
                     Title = a.AttachmentTypeTitle,
                     MemoTypeId = a.MemoTypeId
                 }).ToList();

        return Task.FromResult(q);
    }
}

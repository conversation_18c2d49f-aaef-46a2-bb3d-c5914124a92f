﻿namespace PSSmartMemo.Services;

public class AppDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<List<MenuDto>> GetUserMenus(string user, int moduleId = 1)
    {
        // Get Current Login User
        var dc = contextFactory.CreateDbContext();
        var m = (from a in dc.RoleMenus
            join b in dc.UserRoles on a.RoleId equals b.RoleId
            where b.User.UserId == user &&
                  b.User.IsActive &&
                  a.Menu.IsActive &&
                  a.Menu.ModuleId == moduleId
            orderby a.Menu.SortOrder ?? 0, a.Menu.Name
            select new MenuDto
            {
                Id = a.MenuId,
                Name = a.Menu.Name,
                Url = a.Menu.Url,
                URLTarget = a.Menu.Urltarget,
                ParentId = a.Menu.MenuParentId,
                SortOrder = a.Menu.SortOrder,
                Icon = a.Menu.Icon
            }).Distinct().ToList();

        foreach (var i in m)
        {
            i.HasSubMenu = (from mm in m where mm.ParentId == i.Id select mm).Any();
            if (i.URLTarget == "iframe") i.Url = "/epage?url=" + Uri.EscapeDataString(i.Url);
        }


        return Task.FromResult(m);

        // var q = (from a in dc.Menus
        //              where a.IsActive
        //              orderby a.SortOrder, a.Name
        //              select new MenuDTO
        //              {
        //                  Id = a.Id,
        //                  Name = a.Name,
        //                  Url = a.Url,
        //                  URLTarget = a.Urltarget,
        //                  ParentId = a.MenuParentId, SortOrder = a.SortOrder,
        //              }).ToList();
        //
        //     foreach (var i in q)
        //     {
        //         i.HasSubMenu = (from mm in q where mm.ParentId == i.Id select mm).Any();
        //     }
        //
        //     return Task.FromResult(q);
    }

    public static string ConvertToUrl(string filePath)
    {
        if (string.IsNullOrEmpty(filePath)) return "";
        // find last index of "wwwroot"

        var wwwrootIndex = filePath.LastIndexOf("wwwroot", StringComparison.OrdinalIgnoreCase);
        if (wwwrootIndex != -1)
        {
            // Remove everything before and including "wwwroot"
            var relativePath = filePath.Substring(wwwrootIndex + 8); // 8 is the length of "wwwroot\"

            // Replace backslashes with forward slashes
            relativePath = relativePath.Replace('\\', '/');

            // Add leading forward slash
            var x = "/" + relativePath.TrimStart('/');
            return x;
        }

        // Return original path if "wwwroot" is not found
        return filePath;
    }


    public Task<string> GetCategoryTitle(int categoryId)
    {
        var dc = contextFactory.CreateDbContext();

        var q = dc.Categories.FirstOrDefault(m => m.CategoryId == categoryId);
        if (q == null) return Task.FromResult("");

        return Task.FromResult(" - " + q.CategoryTitle);
    }


    public Task<string> GetUserFullName(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.Users
            .AsNoTracking()
            .FirstOrDefault(m => m.UserId == userId);

        if (q == null) return Task.FromResult(userId);
        return Task.FromResult(q.Name);
    }


    public Task<List<SubCategoryDto>> SetAllSubCategories(int categoryId)
    {
        var dc = contextFactory.CreateDbContext();

        var q = (from a in dc.SubCategories
            orderby a.Title
            where a.CategoryId == categoryId
            select new SubCategoryDto
            {
                Id = a.Id,
                Title = a.Title
            }).ToList();
        return Task.FromResult(q);
    }


    public Task<List<ModuleDto>> GetModules()
    {
        var dc = contextFactory.CreateDbContext();

        var q = (from a in dc.Modules
            where a.IsActive == true && a.Id >= 5
            select new ModuleDto
            {
                Id = a.Id,
                Name = a.ModuleTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<bool> IsUserAdmin(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var isAdmin = (from u in dc.Users
                       where u.UserId.ToLower() == userId.ToLower() && u.IsActive == true
                       select u.IsAdmin).FirstOrDefault();
        return Task.FromResult(isAdmin);
    }
}

using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace PSSmartMemo.Services;

public class EncryptionService
{
    // Hardcoded encryption key - in production, this should be stored securely
    private static readonly string EncryptionKey = "PSSmartMemo2024!@#$%^&*()_+{}|:<>?[]\\;',./`~";
    
    /// <summary>
    /// Encrypts and compresses the given text using AES encryption with MemoId as salt
    /// </summary>
    /// <param name="plainText">Text to encrypt</param>
    /// <param name="memoId">MemoId used as salt</param>
    /// <returns>Base64 encoded encrypted and compressed data</returns>
    public static string EncryptAndCompress(string plainText, int memoId)
    {
        if (string.IsNullOrEmpty(plainText))
            return string.Empty;

        try
        {
            // First compress the text
            var compressedData = CompressString(plainText);
            
            // Generate salt from MemoId
            var salt = GenerateSalt(memoId);
            
            // Derive key from the hardcoded key and salt
            var key = DeriveKey(EncryptionKey, salt);
            
            // Encrypt the compressed data
            var encryptedData = EncryptData(compressedData, key);
            
            // Combine salt and encrypted data
            var result = new byte[salt.Length + encryptedData.Length];
            Array.Copy(salt, 0, result, 0, salt.Length);
            Array.Copy(encryptedData, 0, result, salt.Length, encryptedData.Length);
            
            return Convert.ToBase64String(result);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to encrypt data: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// Decrypts and decompresses the given encrypted text using AES decryption with MemoId as salt
    /// </summary>
    /// <param name="encryptedText">Base64 encoded encrypted and compressed data</param>
    /// <param name="memoId">MemoId used as salt</param>
    /// <returns>Original plain text</returns>
    public static string DecryptAndDecompress(string encryptedText, int memoId)
    {
        if (string.IsNullOrEmpty(encryptedText))
            return string.Empty;

        try
        {
            var encryptedData = Convert.FromBase64String(encryptedText);
            
            // Extract salt (first 16 bytes)
            var salt = new byte[16];
            Array.Copy(encryptedData, 0, salt, 0, 16);
            
            // Extract encrypted content (remaining bytes)
            var encrypted = new byte[encryptedData.Length - 16];
            Array.Copy(encryptedData, 16, encrypted, 0, encrypted.Length);
            
            // Derive key from the hardcoded key and salt
            var key = DeriveKey(EncryptionKey, salt);
            
            // Decrypt the data
            var compressedData = DecryptData(encrypted, key);
            
            // Decompress the data
            var plainText = DecompressString(compressedData);
            
            return plainText;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to decrypt data: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// Generates a consistent salt from MemoId
    /// </summary>
    private static byte[] GenerateSalt(int memoId)
    {
        // Create a consistent salt based on MemoId
        var saltString = $"PSSmartMemo_Salt_{memoId}_2024";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltString));
        
        // Take first 16 bytes for salt
        var salt = new byte[16];
        Array.Copy(hash, 0, salt, 0, 16);
        return salt;
    }
    
    /// <summary>
    /// Derives encryption key from master key and salt using PBKDF2
    /// </summary>
    private static byte[] DeriveKey(string masterKey, byte[] salt)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(masterKey, salt, 10000, HashAlgorithmName.SHA256);
        return pbkdf2.GetBytes(32); // 256-bit key
    }
    
    /// <summary>
    /// Encrypts data using AES-256-CBC
    /// </summary>
    private static byte[] EncryptData(byte[] data, byte[] key)
    {
        using var aes = Aes.Create();
        aes.Key = key;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;
        aes.GenerateIV();
        
        using var encryptor = aes.CreateEncryptor();
        using var msEncrypt = new MemoryStream();
        
        // Write IV first
        msEncrypt.Write(aes.IV, 0, aes.IV.Length);
        
        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
        {
            csEncrypt.Write(data, 0, data.Length);
        }
        
        return msEncrypt.ToArray();
    }
    
    /// <summary>
    /// Decrypts data using AES-256-CBC
    /// </summary>
    private static byte[] DecryptData(byte[] encryptedData, byte[] key)
    {
        using var aes = Aes.Create();
        aes.Key = key;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;
        
        // Extract IV (first 16 bytes)
        var iv = new byte[16];
        Array.Copy(encryptedData, 0, iv, 0, 16);
        aes.IV = iv;
        
        // Extract encrypted content (remaining bytes)
        var encrypted = new byte[encryptedData.Length - 16];
        Array.Copy(encryptedData, 16, encrypted, 0, encrypted.Length);
        
        using var decryptor = aes.CreateDecryptor();
        using var msDecrypt = new MemoryStream(encrypted);
        using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
        using var msResult = new MemoryStream();
        
        csDecrypt.CopyTo(msResult);
        return msResult.ToArray();
    }
    
    /// <summary>
    /// Compresses string using GZip
    /// </summary>
    private static byte[] CompressString(string text)
    {
        var bytes = Encoding.UTF8.GetBytes(text);
        using var output = new MemoryStream();
        using (var gzip = new GZipStream(output, CompressionLevel.Optimal))
        {
            gzip.Write(bytes, 0, bytes.Length);
        }
        return output.ToArray();
    }
    
    /// <summary>
    /// Decompresses string using GZip
    /// </summary>
    private static string DecompressString(byte[] compressedData)
    {
        using var input = new MemoryStream(compressedData);
        using var gzip = new GZipStream(input, CompressionMode.Decompress);
        using var output = new MemoryStream();
        
        gzip.CopyTo(output);
        return Encoding.UTF8.GetString(output.ToArray());
    }
}

﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "ApplicationDbContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Model",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "PSSmartMemo",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[ApprovalActions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[AttachmentTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Categories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Delegations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmpLocationTiming]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FormStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FormTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FormTypeStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Locations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoApprovalLogs]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoApproverRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoApprovers]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoAttachments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Memos]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoSections]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoTemplateApprovers]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoTemplates]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoTemplateSections]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoTemplateUserRoleAssign]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MemoTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Menus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Modules]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[OfficeLocation]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ReportUsersMemoTemplates]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RoleMenus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Roles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ShiftLocations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SubCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TemplateStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Users]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}
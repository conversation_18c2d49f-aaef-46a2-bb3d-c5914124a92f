// Print Optimization JavaScript for Memo Preview

window.optimizeForPrint = function() {
    // Remove any existing print optimization classes
    document.querySelectorAll('.print-optimized').forEach(el => {
        el.classList.remove('print-optimized');
    });

    // Add print optimization class to main container
    const container = document.getElementById('printable-content');
    if (container) {
        container.classList.add('print-optimized');
    }

    // Optimize section breaks
    optimizeSectionBreaks();
    
    // Optimize table breaks
    optimizeTableBreaks();
    
    // Optimize approver grid for print
    optimizeApproverGrid();
    
    // Handle long content sections
    handleLongContent();
};

function optimizeSectionBreaks() {
    const sections = document.querySelectorAll('.print-section-block, .tmp-preview-section-block');

    sections.forEach((section, index) => {
        // Remove any existing page break classes
        section.classList.remove('page-break-before', 'page-break-after', 'keep-together');

        // Calculate section height
        const sectionHeight = section.offsetHeight;
        const pageHeight = 1056; // Approximate A4 page height in pixels at 96 DPI

        // Always allow sections to break for content flow
        section.style.pageBreakInside = 'auto';
        section.style.breakInside = 'auto';
        section.style.overflow = 'visible';

        // Ensure section headers stay with some content
        const header = section.querySelector('.print-section-header, .tmp-preview-section-header');
        if (header) {
            header.style.pageBreakAfter = 'avoid';
            header.style.breakAfter = 'avoid';
            header.style.pageBreakInside = 'avoid';
            header.style.breakInside = 'avoid';
        }

        // Allow section content to flow naturally
        const content = section.querySelector('.print-section-content, .tmp-preview-section-content');
        if (content) {
            content.style.pageBreakInside = 'auto';
            content.style.breakInside = 'auto';
            content.style.overflow = 'visible';

            // Handle very large content sections
            if (sectionHeight > pageHeight * 0.6) {
                // For large sections, ensure proper text flow
                const paragraphs = content.querySelectorAll('p');
                paragraphs.forEach(p => {
                    p.style.pageBreakInside = 'auto';
                    p.style.breakInside = 'auto';
                    p.style.orphans = '2';
                    p.style.widows = '2';
                });

                // Handle lists in large sections
                const lists = content.querySelectorAll('ul, ol');
                lists.forEach(list => {
                    list.style.pageBreakInside = 'auto';
                    list.style.breakInside = 'auto';
                });
            }
        }
    });
}

function optimizeTableBreaks() {
    const tables = document.querySelectorAll('.print-attachments-table');
    
    tables.forEach(table => {
        // Allow table to break across pages if needed
        table.style.pageBreakInside = 'auto';
        table.style.breakInside = 'auto';
        
        // Keep table headers with content
        const thead = table.querySelector('thead');
        if (thead) {
            thead.style.pageBreakAfter = 'avoid';
            thead.style.breakAfter = 'avoid';
        }
        
        // Prevent orphaned rows
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.style.pageBreakInside = 'avoid';
            row.style.breakInside = 'avoid';
        });
    });
}

function optimizeApproverGrid() {
    const approverGrid = document.querySelector('.approval-grid');
    if (approverGrid) {
        // For print, convert grid to a more print-friendly layout
        const approverBoxes = approverGrid.querySelectorAll('.approval-box');
        
        approverBoxes.forEach((box, index) => {
            // Ensure each approver box doesn't break across pages
            box.style.pageBreakInside = 'avoid';
            box.style.breakInside = 'avoid';
            
            // Add some spacing for better readability
            if (index > 0 && index % 3 === 0) {
                box.style.pageBreakBefore = 'auto';
            }
        });
    }
}

function handleLongContent() {
    const contentSections = document.querySelectorAll('.print-section-content, .tmp-preview-section-content');

    contentSections.forEach(content => {
        // Ensure content can flow across pages
        content.style.pageBreakInside = 'auto';
        content.style.breakInside = 'auto';
        content.style.overflow = 'visible';

        // Handle long paragraphs
        const paragraphs = content.querySelectorAll('p');
        paragraphs.forEach(p => {
            // Allow paragraphs to break but prevent orphans and widows
            p.style.pageBreakInside = 'auto';
            p.style.breakInside = 'auto';
            p.style.orphans = '2';
            p.style.widows = '2';
        });

        // Handle lists - allow breaking for long lists
        const lists = content.querySelectorAll('ul, ol');
        lists.forEach(list => {
            // Always allow list breaking for better flow
            list.style.pageBreakInside = 'auto';
            list.style.breakInside = 'auto';

            // Handle list items
            const listItems = list.querySelectorAll('li');
            listItems.forEach(li => {
                li.style.pageBreakInside = 'auto';
                li.style.breakInside = 'auto';
            });
        });

        // Handle headings - keep with following content
        const headings = content.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            heading.style.pageBreakAfter = 'avoid';
            heading.style.breakAfter = 'avoid';
            heading.style.pageBreakInside = 'avoid';
            heading.style.breakInside = 'avoid';
        });

        // Handle tables within content
        const tables = content.querySelectorAll('table');
        tables.forEach(table => {
            table.style.pageBreakInside = 'auto';
            table.style.breakInside = 'auto';

            // Handle table rows
            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                row.style.pageBreakInside = 'avoid';
                row.style.breakInside = 'avoid';
            });
        });

        // Handle divs and other block elements
        const blockElements = content.querySelectorAll('div, section, article');
        blockElements.forEach(block => {
            block.style.pageBreakInside = 'auto';
            block.style.breakInside = 'auto';
            block.style.overflow = 'visible';
        });
    });
}

// Add print event listeners
window.addEventListener('beforeprint', function() {
    console.log('Preparing for print...');
    optimizeForPrint();
});

window.addEventListener('afterprint', function() {
    console.log('Print completed');
    // Clean up any print-specific modifications if needed
});

// CSS injection for additional print styles
function injectPrintStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @media print {
            /* Additional dynamic print styles */
            .print-optimized {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            /* Ensure proper page margins */
            @page {
                margin: 0.75in;
                size: A4 portrait;
            }
            
            /* Force page breaks where needed */
            .force-page-break {
                page-break-before: always !important;
                break-before: page !important;
            }
            
            /* Prevent page breaks */
            .keep-together {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }
            
            /* Optimize text for print */
            body {
                font-size: 12pt !important;
                line-height: 1.4 !important;
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize print optimization when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectPrintStyles);
} else {
    injectPrintStyles();
}
